package com.bto;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.swagger2.annotations.EnableSwagger2WebMvc;

/**
 * SpringBoot方式启动类
 */
@Slf4j
@EnableSwagger2WebMvc
@RestController
@SpringBootApplication
public class EnergyApplication {

    /* 解决druid 日志报错：discard long time none received connection:xxx */
    static {
        System.setProperty("druid.mysql.usePingMethod","false");
    }

    /**
     * 主启动函数
     */
    public static void main(String[] args) {
        SpringApplication springApplication = new SpringApplication(EnergyApplication.class);
        springApplication.setBannerMode(Banner.Mode.OFF);
        springApplication.run(args);
        log.info(">>> {}", EnergyApplication.class.getSimpleName().toUpperCase() + " STARTING SUCCESS");
    }

    /**
     * 首页
     **/
    @GetMapping("/")
    public String index() {
        return "WELCOME";
    }
}
