#########################################
# server configuration
#########################################
server.port=82

#########################################
# spring profiles configuration
#########################################
spring.profiles.active=local
#spring.profiles.active=test
#spring.profiles.active=prod

#########################################
# multipart configuration
#########################################
spring.servlet.multipart.max-request-size=100MB
spring.servlet.multipart.max-file-size=100MB

#########################################
# datasource configuration
#########################################

# mysql
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.datasource.dynamic.datasource.master.url=**********************************************************************************************************************************************************************************
spring.datasource.dynamic.datasource.master.url=jdbc:mysql://************:3306/bto_energy_management?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&useInformationSchema=true
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=123
spring.datasource.dynamic.strict=true

# druid monitor configuration
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.login-username=bto
spring.datasource.druid.stat-view-servlet.login-password=bto2024

# druid global configuration
spring.datasource.dynamic.public-key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMWiTVtdXFVrgFHDDKELZM0SywkWY3KjugN90eY5Sogon1j8Y0ClPF7nx3FuE7pAeBKiv7ChIS0vvx/59WUpKmUCAwEAAQ==
spring.datasource.dynamic.druid.initial-size=5
spring.datasource.dynamic.druid.max-active=20
spring.datasource.dynamic.druid.min-idle=5
spring.datasource.dynamic.druid.max-wait=60000
spring.datasource.dynamic.druid.pool-prepared-statements=true
spring.datasource.dynamic.druid.max-pool-prepared-statement-per-connection-size=20
spring.datasource.dynamic.druid.validation-query-timeout=2000
spring.datasource.dynamic.druid.test-on-borrow=false
spring.datasource.dynamic.druid.test-on-return=false
spring.datasource.dynamic.druid.test-while-idle=true
spring.datasource.dynamic.druid.time-between-eviction-runs-millis=60000
spring.datasource.dynamic.druid.min-evictable-idle-time-millis=300000
spring.datasource.dynamic.druid.filters=stat
spring.datasource.dynamic.druid.break-after-acquire-failure=false

#########################################
# jackson configuration
#########################################
spring.jackson.time-zone=GMT+8
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.locale=zh_CN

#########################################
# redis configuration
#########################################
spring.redis.database=7
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=
spring.redis.timeout=10s

spring.redis.lettuce.pool.max-active=200
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.min-idle=0

#########################################
# mybatis-plus configuration
#########################################
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.configuration.jdbc-type-for-null=null
mybatis-plus.global-config.banner=false
mybatis-plus.global-config.enable-sql-runner=true
mybatis-plus.global-config.db-config.id-type=ASSIGN_ID
mybatis-plus.global-config.db-config.logic-delete-field=DELETE_FLAG
mybatis-plus.global-config.db-config.logic-delete-value=DELETED
mybatis-plus.global-config.db-config.logic-not-delete-value=NOT_DELETE
mybatis-plus.mapper-locations=classpath*:com/bto/**/mapping/*.xml
mybatis-plus.type-handlers-package=com.bto.common.handler

#########################################
# easy-trans configuration
#########################################
easy-trans.is-enable-redis=true
easy-trans.is-enable-global=true
easy-trans.is-enable-tile=true
easy-trans.is-enable-cloud=false

#########################################
# sa-token configuration
#########################################
sa-token.token-name=token
sa-token.timeout=2592000
sa-token.activity-timeout=-1
sa-token.is-concurrent=true
sa-token.is-share=false
sa-token.max-login-count=-1
sa-token.token-style=random-32
sa-token.is-log=false
sa-token.is-print=false

# sa-token alone-redis configuration
sa-token.alone-redis.database=2
sa-token.alone-redis.host=${spring.redis.host}
sa-token.alone-redis.port=${spring.redis.port}
sa-token.alone-redis.password=${spring.redis.password}
sa-token.alone-redis.timeout=${spring.redis.timeout}
sa-token.alone-redis.lettuce.pool.max-active=${spring.redis.lettuce.pool.max-active}
sa-token.alone-redis.lettuce.pool.max-wait=${spring.redis.lettuce.pool.max-wait}
sa-token.alone-redis.lettuce.pool.max-idle=${spring.redis.lettuce.pool.max-idle}
sa-token.alone-redis.lettuce.pool.min-idle=${spring.redis.lettuce.pool.min-idle}

#########################################
# knife4j configuration
#########################################
knife4j.enable=true
knife4j.production=false
knife4j.basic.enable=true
knife4j.basic.username=bto
knife4j.basic.password=bto2024
knife4j.setting.enableOpenApi=false
knife4j.setting.enableSwaggerModels=false
knife4j.setting.enableFooter=false
knife4j.setting.enableFooterCustom=true
knife4j.setting.footerCustomContent=Apache License 2.0 | Copyright 2022-[BTO](https://www.btosolar.com)

# common configuration
bto.config.common.front-url=http://localhost:81
bto.config.common.backend-url=http://localhost:82
