# BTO 能源管理平台 API 文档

## 接口概览

BTO能源管理平台提供RESTful API接口，支持JSON格式数据交互。所有接口都遵循统一的响应格式和错误处理机制。

### 基础信息

- **Base URL**: `http://localhost:82`
- **API文档**: `http://localhost:82/doc.html`
- **认证方式**: <PERSON><PERSON> (Sa-Token)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 统一响应格式

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {}
}
```

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 401 | 未登录 |
| 403 | 无权限 |
| 404 | 路径不存在 |
| 405 | 请求方法不正确 |
| 500 | 业务异常 |

## 认证接口

### 1. 用户登录

**接口地址**: `POST /auth/b/login`

**请求参数**:
```json
{
  "account": "admin",
  "password": "123456",
  "validCode": "1234",
  "validCodeReqNo": "uuid"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "userInfo": {
      "id": "1",
      "account": "admin",
      "name": "管理员",
      "avatar": "http://...",
      "orgId": "1",
      "orgName": "总公司"
    }
  }
}
```

### 2. 获取验证码

**接口地址**: `GET /auth/b/getValidCode`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "validCodeReqNo": "uuid-string",
    "validCodeBase64": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
  }
}
```

### 3. 退出登录

**接口地址**: `POST /auth/b/logout`

**请求头**: `Authorization: Bearer {token}`

**响应示例**:
```json
{
  "code": 200,
  "msg": "退出成功",
  "data": null
}
```

## 储能系统接口

### 1. 电站档案管理

#### 获取电站分页列表

**接口地址**: `GET /stored/plantBase/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| plantName | String | 否 | 电站名称 |
| plantTypeId | Integer | 否 | 电站类型 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "plantUid": "BTO-UUID-001",
        "plantName": "示例电站",
        "plantCapacity": 1000000,
        "plantTypeId": 1,
        "province": "广东省",
        "city": "深圳市",
        "createTime": "2024-08-09 10:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 新增电站档案

**接口地址**: `POST /stored/plantBase/add`

**请求参数**:
```json
{
  "plantName": "新电站",
  "plantCapacity": 1000000,
  "plantTypeId": 1,
  "province": "广东省",
  "city": "深圳市",
  "county": "南山区",
  "address": "详细地址",
  "longitude": "113.123456",
  "latitude": "22.123456",
  "contactName": "联系人",
  "contactPhone": "***********"
}
```

### 2. 设备信息管理

#### 获取设备分页列表

**接口地址**: `GET /stored/device/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码 |
| size | Integer | 否 | 每页条数 |
| plantUid | String | 否 | 电站UID |
| deviceType | Integer | 否 | 设备类型 |
| deviceId | String | 否 | 设备编号 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "plantUid": "BTO-UUID-001",
        "deviceId": "INV001",
        "deviceType": 1,
        "manufacturer": "华为",
        "deviceModel": "SUN2000-60KTL",
        "ratedPower": 60000,
        "installAddress": "1号厂房屋顶",
        "startTime": "2024-01-01 00:00:00",
        "cardStatus": 1
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 新增设备信息

**接口地址**: `POST /stored/device/add`

**请求参数**:
```json
{
  "plantUid": "BTO-UUID-001",
  "deviceId": "INV002",
  "deviceType": 1,
  "manufacturer": "华为",
  "deviceModel": "SUN2000-60KTL",
  "ratedPower": 60000,
  "installAddress": "2号厂房屋顶"
}
```

### 3. 电池数据监控

#### 获取电池数据分页

**接口地址**: `GET /stored/battery/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码 |
| size | Integer | 否 | 每页条数 |
| inverterSn | String | 否 | 逆变器SN |
| startTime | String | 否 | 开始时间 |
| endTime | String | 否 | 结束时间 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "inverterSn": "INV001",
        "collectionTime": "2024-08-09 14:30:00",
        "mpvMode": 2,
        "batteryDirection": 1,
        "batteryPower": 5000,
        "batterySoc": 85,
        "batteryVoltage": 48.5,
        "batteryCurrent": 103.1,
        "batteryTemp": 25.5
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 4. 逆变器数据监控

#### 获取逆变器数据分页

**接口地址**: `GET /stored/inverter/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码 |
| size | Integer | 否 | 每页条数 |
| inverterSn | String | 否 | 逆变器SN |
| tableName | String | 是 | 表名后缀(如:20240809) |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": 1,
        "inverterSn": "INV001",
        "power": 45000,
        "todayElectricity": 25000,
        "monthElectricity": 750000,
        "yearElectricity": 9000000,
        "totalElectricity": 18000000,
        "collectionTime": "2024-08-09 14:30:00"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 5. 能源统计接口

#### 获取能源统计信息

**接口地址**: `GET /stored/statistics/info`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalPower": 1500000,
    "todayGeneration": 8500,
    "monthGeneration": 255000,
    "yearGeneration": 3060000,
    "totalGeneration": 6120000,
    "onlineDevices": 25,
    "totalDevices": 30,
    "onlineRate": 83.33,
    "updateTime": "2024-08-09 14:35:00"
  }
}
```

#### 获取电池统计信息

**接口地址**: `GET /stored/statistics/battery`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCapacity": 500000,
    "availableCapacity": 425000,
    "chargingPower": 15000,
    "dischargingPower": 8000,
    "averageSoc": 85,
    "batteryCount": 10,
    "healthyCount": 9,
    "warningCount": 1
  }
}
```

## 系统管理接口

### 1. 用户管理

#### 获取用户分页列表

**接口地址**: `GET /sys/user/page`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 当前页码 |
| size | Integer | 否 | 每页条数 |
| searchKey | String | 否 | 搜索关键字 |
| userStatus | String | 否 | 用户状态 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": "1",
        "avatar": "http://...",
        "account": "admin",
        "name": "管理员",
        "nickname": "超级管理员",
        "gender": "MALE",
        "age": 30,
        "phone": "***********",
        "email": "<EMAIL>",
        "userStatus": "ENABLE",
        "orgId": "1",
        "orgName": "总公司",
        "createTime": "2024-01-01 00:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

#### 新增用户

**接口地址**: `POST /sys/user/add`

**请求参数**:
```json
{
  "account": "testuser",
  "name": "测试用户",
  "nickname": "测试",
  "gender": "MALE",
  "age": 25,
  "phone": "***********",
  "email": "<EMAIL>",
  "orgId": "1",
  "positionId": "1"
}
```

### 2. 角色管理

#### 获取角色分页列表

**接口地址**: `GET /sys/role/page`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": "1",
        "name": "超级管理员",
        "code": "SUPER_ADMIN",
        "category": "GLOBAL",
        "sortCode": 1,
        "remark": "系统超级管理员",
        "createTime": "2024-01-01 00:00:00"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

## 错误处理

### 常见错误码

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 401 | 未登录 | Token无效或已过期 |
| 403 | 无权限 | 用户无访问权限 |
| 404 | 路径不存在 | 请求的接口不存在 |
| 500 | 业务异常 | 服务器内部错误 |

### 错误响应示例

```json
{
  "code": 500,
  "msg": "设备信息不存在，id值为：INV999",
  "data": null
}
```

## 接口调用示例

### JavaScript (Axios)

```javascript
// 设置请求拦截器
axios.interceptors.request.use(config => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 登录
const login = async (account, password) => {
  const response = await axios.post('/auth/b/login', {
    account,
    password
  });
  return response.data;
};

// 获取电站列表
const getPlantList = async (params) => {
  const response = await axios.get('/stored/plantBase/page', { params });
  return response.data;
};
```

### Java (OkHttp)

```java
// 创建客户端
OkHttpClient client = new OkHttpClient();

// 登录请求
RequestBody body = RequestBody.create(
    MediaType.parse("application/json"),
    "{\"account\":\"admin\",\"password\":\"123456\"}"
);

Request request = new Request.Builder()
    .url("http://localhost:82/auth/b/login")
    .post(body)
    .build();

Response response = client.newCall(request).execute();
```

### Python (Requests)

```python
import requests

# 登录
def login(account, password):
    url = "http://localhost:82/auth/b/login"
    data = {
        "account": account,
        "password": password
    }
    response = requests.post(url, json=data)
    return response.json()

# 获取设备列表
def get_device_list(token, params=None):
    url = "http://localhost:82/stored/device/page"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    response = requests.get(url, headers=headers, params=params)
    return response.json()
```
