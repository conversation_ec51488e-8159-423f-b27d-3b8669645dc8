package com.bto.sys.modular.position.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.sys.modular.position.entity.SysPosition;
import org.apache.ibatis.annotations.Param;

/**
 * 职位Mapper接口
 *
 **/
public interface SysPositionMapper extends BaseMapper<SysPosition> {

    /**
     * 删除数据并忽略插件（逻辑删除、租户拼接）
     *
     
     */
    @InterceptorIgnore(tenantLine = "true")
    void deleteIgnoreInterceptor(@Param("ew") LambdaQueryWrapper<SysPosition> lambdaQueryWrapper);
}
