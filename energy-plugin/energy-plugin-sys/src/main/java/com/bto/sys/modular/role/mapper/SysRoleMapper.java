package com.bto.sys.modular.role.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.sys.modular.role.entity.SysRole;
import org.apache.ibatis.annotations.Param;

/**
 * 角色Mapper接口
 *
 **/
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 删除数据并忽略插件（逻辑删除、租户拼接）
     *
     
     */
    @InterceptorIgnore(tenantLine = "true")
    void deleteIgnoreInterceptor(@Param("ew") LambdaQueryWrapper<SysRole> lambdaQueryWrapper);
}
