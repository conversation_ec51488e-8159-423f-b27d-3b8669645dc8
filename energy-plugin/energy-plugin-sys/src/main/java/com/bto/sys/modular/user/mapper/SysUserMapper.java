package com.bto.sys.modular.user.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.sys.modular.user.entity.SysUser;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper接口
 *
 **/
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 删除数据并忽略插件（逻辑删除、租户拼接）
     *
     
     */
    @InterceptorIgnore(tenantLine = "true")
    void deleteIgnoreInterceptor(@Param("ew") LambdaQueryWrapper<SysUser> lambdaQueryWrapper);
}
