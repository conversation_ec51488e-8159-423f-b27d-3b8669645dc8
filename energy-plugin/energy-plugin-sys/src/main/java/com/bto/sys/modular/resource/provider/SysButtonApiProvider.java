package com.bto.sys.modular.resource.provider;

import com.bto.sys.api.SysButtonApi;
import com.bto.sys.modular.resource.service.SysButtonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 按钮API接口实现类
 *
 **/
@Service
public class SysButtonApiProvider implements SysButtonApi {

    @Resource
    private SysButtonService sysButtonService;

    @Override
    public void addForGenButton(String menuId, String className, String functionName) {
        sysButtonService.addForGenButton(menuId, className, functionName);
    }
}
