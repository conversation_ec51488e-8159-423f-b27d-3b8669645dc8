package com.bto.sys.modular.org.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.sys.modular.org.entity.SysOrg;
import org.apache.ibatis.annotations.Param;

/**
 * 组织Mapper接口
 *
 **/
public interface SysOrgMapper extends BaseMapper<SysOrg> {

    /**
     * 删除数据并忽略插件（逻辑删除、租户拼接）
     *
     
     */
    @InterceptorIgnore(tenantLine = "true")
    void deleteIgnoreInterceptor(@Param("ew") LambdaQueryWrapper<SysOrg> lambdaQueryWrapper);
}
