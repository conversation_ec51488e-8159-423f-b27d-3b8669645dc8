package com.bto.biz.modular.dict.enums;

import lombok.Getter;
import com.bto.common.exception.CommonException;

/**
 * 业务字典分类枚举
 */
@Getter
public enum BizDictCategoryEnum {

    /**
     * 业务
     */
    BIZ("BIZ");

    private final String value;

    BizDictCategoryEnum(String value) {
        this.value = value;
    }

    public static void validate(String value) {
        boolean flag = BIZ.getValue().equals(value);
        if(!flag) {
            throw new CommonException("不支持的字典分类：{}", value);
        }
    }
}
