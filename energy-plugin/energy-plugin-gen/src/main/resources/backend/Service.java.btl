package ${packageName}.${moduleName}.modular.${busName}.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import ${packageName}.${moduleName}.modular.${busName}.entity.${className};
import ${packageName}.${moduleName}.modular.${busName}.param.${className}AddParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}EditParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}IdParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}PageParam;

import java.util.List;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 **/
public interface ${className}Service extends IService<${className}> {

    /**
     * 获取${functionName}分页
     *
     * <AUTHOR>
     **/
    ${className} queryEntity(String id);
}
