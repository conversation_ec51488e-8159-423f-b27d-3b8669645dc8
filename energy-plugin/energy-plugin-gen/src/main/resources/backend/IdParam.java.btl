package ${packageName}.${moduleName}.modular.${busName}.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * ${functionName}Id参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class ${className}IdParam {

    /** ${dbTableKeyRemark} */
    @ApiModelProperty(value = "${dbTableKeyRemark}", required = true)
    @NotBlank(message = "${dbTableKeyCamelCase}不能为空")
    private ${dbTableKeyJavaType} ${dbTableKeyCamelCase};
}
