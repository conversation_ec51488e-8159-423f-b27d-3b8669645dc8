package ${packageName}.${moduleName}.modular.${busName}.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ${functionName}编辑参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class ${className}EditParam {

    <% for(var i = 0; i < configList.~size; i++) { %>
    <% if(configList[i].needEdit) { %>
    /** ${configList[i].fieldRemark} */
    @ApiModelProperty(value = "${configList[i].fieldRemark}",<% if(configList[i].required) { %> required = true,<% } %> position = ${i + 1})
    <% if(configList[i].required) { %>
    <% if(configList[i].fieldJavaType == 'String') { %>@NotBlank<% } else { %>@NotNull<% } %>(message = "${configList[i].fieldNameCamelCase}不能为空")
    <% } else { %><% } %>
    private ${configList[i].fieldJavaType} ${configList[i].fieldNameCamelCase};

    <% } %>
    <% } %>
}
