<template>
    <%
    var searchCount = 0;
    var row = 0;
    %>
    <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId && configList[i].needPage) { searchCount ++; }%>
    <% } %>
    <% if (searchCount > 0) { %>
    <% for(var i = 0; i < configList.~size; i++) { %>
    <% if(!configList[i].needTableId && configList[i].needPage) { row ++; %>
    <% if(row <= 1) { %>
    <snowy-search placeholder="请输入${configList[i].fieldRemark}" v-model="searchFormState.${configList[i].fieldNameCamelCase}" @confirm="loadData(true)" @clear="loadData(true)" :enableSenior="true" @seniorSearch="$refs.searchRef.open()"></snowy-search>
    <% } %>
    <% } %>
    <% } %>
    <search ref="searchRef" :searchFormState="searchFormState" @confirm="loadData(true)"></search>
    <% } %>
    <view class="${busName}-list">
        <view class="item" v-for="(item, index) in ${classNameFirstLower}Data" :key="index" :index="index" @tap="moreTapItem(item, index)">
            <% for(var i = 0; i < configList.~size; i++) { %>
            <% if(!configList[i].needTableId && configList[i].whetherTable && configList[i].fieldNameCamelCase != 'tenantId') { %>
            <uv-row customStyle="margin-bottom: 15rpx">
                <uv-col :span="1">
                    <uv-icon size="18" name="tags-fill" color="#5677fc"></uv-icon>
                </uv-col>
                <uv-col :span="5">
                    <view class="item-left">${configList[i].fieldRemark}</view>
                </uv-col>
                <uv-col :span="6" textAlign="right">
                    <view class="item-right snowy-bold snowy-ellipsis"> {{ item.${configList[i].fieldNameCamelCase} }} </view>
                </uv-col>
            </uv-row>
            <% } %>
            <% } %>
        </view>
    </view>
    <snowy-empty :fixed="true" v-show="$xeu.isEmpty(${classNameFirstLower}Data)" />
    <snowy-float-btn v-if="$snowy.hasPerm('mobile${className}Add')" @click="add"></snowy-float-btn>
    <more ref="moreRef" @handleOk="loadData(true)"></more>
</template>

<script setup name="${busName}">
    <% if (searchCount > 0) { %>
    import search from './search.vue'
    <% } %>
    import { ${classNameFirstLower}Page } from '@/api/${moduleName}/${classNameFirstLower}Api'
    import more from './more.vue'
    import { onLoad, onShow, onReady, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app"
    import { reactive, ref, getCurrentInstance } from "vue"
    const { proxy } = getCurrentInstance()
    const searchFormState = reactive({
        <% if (searchCount > 0) { %>
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId && configList[i].needPage) { %>
        ${configList[i].fieldNameCamelCase}: '',
        <% } %>
        <% } %>
        <% } %>
    })
    const parameter = reactive({
    	current: 1,
    	size: 10
    })
    const ${classNameFirstLower}Data = ref([])
    const loadData = (isReset) => {
        if (isReset) {
        	parameter.current = 1
        	${classNameFirstLower}Data.value = []
        }
        Object.assign(parameter, searchFormState)
        ${classNameFirstLower}Page(parameter).then(res => {
            if (uni.$xeu.isEmpty(res?.data?.records)){
            	return
            }
            ${classNameFirstLower}Data.value = ${classNameFirstLower}Data.value.concat(res.data.records)
            parameter.current++
        }).finally(()=>{
          	uni.stopPullDownRefresh()
        })
    }
    loadData(true)
    onShow(() => {
    	uni.$once('formBack', (data) => {
            loadData(true)
    	})
    })
    // 下拉刷新
    onPullDownRefresh(() => {
    	loadData(true)
    })
    // 上拉加载
    onReachBottom(() => {
    	loadData()
    })
    // 新增
    const add = () => {
    	uni.navigateTo({
            url: '/pages/${moduleName}/${busName}/form'
    	})
    }
    // 更多操作
    const moreRef = ref()
    const moreTapItem = (item, index) => {
        moreRef.value.open(item)
    }
</script>

<style lang="scss" scoped>
    .${busName}-list {}
    .item {
        background: #ffffff;
        margin: 20rpx 0;
        padding: 25rpx;
        box-shadow: 0 1px 2px #ccc;
        border-radius: 15rpx;
        .item-left {
            color: #999;
            font-size: 26rpx;
        }
        .item-right {
            font-size: 26rpx;
        }
    }
    .item:hover {
        box-shadow: 1upx 5upx 5upx #5677fc;
    }
</style>
