package com.bto.stored.modular.inverter.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 逆变器实时数据数据实体
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@TableName("bto_inverter_20240809")
public class BtoInverter {

    /** 主键id */
    @TableId
    @ApiModelProperty(value = "主键id", position = 1)
    private Long id;

    /** 逆变器SN */
    @ApiModelProperty(value = "逆变器SN", position = 2)
    private String inverterSn;

    /** 功率（W） */
    @ApiModelProperty(value = "功率（W）", position = 3)
    private Integer power;

    /** 当天发电量（kWh）*100 */
    @ApiModelProperty(value = "当天发电量（kWh）*100", position = 4)
    private Integer todayElectricity;

    /** 当月发电量（kWh）*100 */
    @ApiModelProperty(value = "当月发电量（kWh）*100", position = 5)
    private Integer monthElectricity;

    /** 当年发电量（kWh）*100 */
    @ApiModelProperty(value = "当年发电量（kWh）*100", position = 6)
    private Integer yearElectricity;

    /** 累计发电量（kWh）*100 */
    @ApiModelProperty(value = "累计发电量（kWh）*100", position = 7)
    private Integer totalElectricity;

    /** 数据时间 */
    @ApiModelProperty(value = "数据时间", position = 8)
    private Date initTime;

    /** 输入电流1路（A）*100 */
    @ApiModelProperty(value = "输入电流1路（A）*100", position = 9)
    private Integer ipv1;

    /** 输入电流2路（A）*100 */
    @ApiModelProperty(value = "输入电流2路（A）*100", position = 10)
    private Integer ipv2;

    /** 输入电流3路（A）*100 */
    @ApiModelProperty(value = "输入电流3路（A）*100", position = 11)
    private Integer ipv3;

    /** 输入电流4路（A）*100 */
    @ApiModelProperty(value = "输入电流4路（A）*100", position = 12)
    private Integer ipv4;

    /** 输入电流5路（A）*100 */
    @ApiModelProperty(value = "输入电流5路（A）*100", position = 13)
    private Integer ipv5;

    /** 输入电流6路（A）*100 */
    @ApiModelProperty(value = "输入电流6路（A）*100", position = 14)
    private Integer ipv6;

    /** 输入电流7路（A）*100 */
    @ApiModelProperty(value = "输入电流7路（A）*100", position = 15)
    private Integer ipv7;

    /** 输入电流8路（A）*100 */
    @ApiModelProperty(value = "输入电流8路（A）*100", position = 16)
    private Integer ipv8;

    /** 输入电流9路（A）*100 */
    @ApiModelProperty(value = "输入电流9路（A）*100", position = 17)
    private Integer ipv9;

    /** 输入电流10路（A）*100 */
    @ApiModelProperty(value = "输入电流10路（A）*100", position = 18)
    private Integer ipv10;

    /** 输入电流11路（A）*100 */
    @ApiModelProperty(value = "输入电流11路（A）*100", position = 19)
    private Integer ipv11;

    /** 输入电流12路（A）*100 */
    @ApiModelProperty(value = "输入电流12路（A）*100", position = 20)
    private Integer ipv12;

    /** 输入电压1路（V）*100 */
    @ApiModelProperty(value = "输入电压1路（V）*100", position = 21)
    private Integer vpv1;

    /** 输入电压2路（V）*100 */
    @ApiModelProperty(value = "输入电压2路（V）*100", position = 22)
    private Integer vpv2;

    /** 输入电压3路（V）*100 */
    @ApiModelProperty(value = "输入电压3路（V）*100", position = 23)
    private Integer vpv3;

    /** 输入电压4路（V）*100 */
    @ApiModelProperty(value = "输入电压4路（V）*100", position = 24)
    private Integer vpv4;

    /** 输入电压5路（V）*100 */
    @ApiModelProperty(value = "输入电压5路（V）*100", position = 25)
    private Integer vpv5;

    /** 输入电压6路（V）*100 */
    @ApiModelProperty(value = "输入电压6路（V）*100", position = 26)
    private Integer vpv6;

    /** 输入电压7路（V）*100 */
    @ApiModelProperty(value = "输入电压7路（V）*100", position = 27)
    private Integer vpv7;

    /** 输入电压8路（V）*100 */
    @ApiModelProperty(value = "输入电压8路（V）*100", position = 28)
    private Integer vpv8;

    /** 输入电压9路（V）*100 */
    @ApiModelProperty(value = "输入电压9路（V）*100", position = 29)
    private Integer vpv9;

    /** 输入电压10路（V）*100 */
    @ApiModelProperty(value = "输入电压10路（V）*100", position = 30)
    private Integer vpv10;

    /** 输入电压11路（V）*100 */
    @ApiModelProperty(value = "输入电压11路（V）*100", position = 31)
    private Integer vpv11;

    /** 输入电压12路（V）*100 */
    @ApiModelProperty(value = "输入电压12路（V）*100", position = 32)
    private Integer vpv12;

    /** 输出电流1路（A）*100 */
    @ApiModelProperty(value = "输出电流1路（A）*100", position = 33)
    private Integer iac1;

    /** 输出电流2路（A）*100 */
    @ApiModelProperty(value = "输出电流2路（A）*100", position = 34)
    private Integer iac2;

    /** 输出电流3路（A）*100 */
    @ApiModelProperty(value = "输出电流3路（A）*100", position = 35)
    private Integer iac3;

    /** 输出电压1路（V）*100 */
    @ApiModelProperty(value = "输出电压1路（V）*100", position = 36)
    private Integer vac1;

    /** 输出电压2路（V）*100 */
    @ApiModelProperty(value = "输出电压2路（V）*100", position = 37)
    private Integer vac2;

    /** 输出电压3路（V）*100 */
    @ApiModelProperty(value = "输出电压3路（V）*100", position = 38)
    private Integer vac3;

    /** 温度（℃） */
    @ApiModelProperty(value = "温度（℃）", position = 39)
    private String temp;

    /** 频率1（Hz）*100 */
    @ApiModelProperty(value = "频率1（Hz）*100", position = 40)
    private Integer fac1;

    /** 频率2（Hz）*100 */
    @ApiModelProperty(value = "频率2（Hz）*100", position = 41)
    private Integer fac2;

    /** 频率3（Hz）*100 */
    @ApiModelProperty(value = "频率3（Hz）*100", position = 42)
    private Integer fac3;

    /** pv1功率 */
    @ApiModelProperty(value = "pv1功率", position = 43)
    private Integer pv1Power;

    /** pv2功率 */
    @ApiModelProperty(value = "pv2功率", position = 44)
    private Integer pv2Power;

    /** pv3功率 */
    @ApiModelProperty(value = "pv3功率", position = 45)
    private Integer pv3Power;

    /** pv4功率 */
    @ApiModelProperty(value = "pv4功率", position = 46)
    private Integer pv4Power;

    /** pv5功率 */
    @ApiModelProperty(value = "pv5功率", position = 47)
    private Integer pv5Power;

    /** pv6功率 */
    @ApiModelProperty(value = "pv6功率", position = 48)
    private Integer pv6Power;

    /** pv7功率 */
    @ApiModelProperty(value = "pv7功率", position = 49)
    private Integer pv7Power;

    /** pv8功率 */
    @ApiModelProperty(value = "pv8功率", position = 50)
    private Integer pv8Power;

    /** pv9功率 */
    @ApiModelProperty(value = "pv9功率", position = 51)
    private Integer pv9Power;

    /** pv10功率 */
    @ApiModelProperty(value = "pv10功率", position = 52)
    private Integer pv10Power;

    /** pv11功率 */
    @ApiModelProperty(value = "pv11功率", position = 53)
    private Integer pv11Power;

    /** pv12功率 */
    @ApiModelProperty(value = "pv12功率", position = 54)
    private Integer pv12Power;

    /** 数据标识（三晶：0，自取：1） */
    @ApiModelProperty(value = "数据标识（三晶：0，自取：1）", position = 55)
    private Integer state;

    /** 数据插入时间 */
    @ApiModelProperty(value = "数据插入时间", position = 56)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}