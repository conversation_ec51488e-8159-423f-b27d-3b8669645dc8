package com.bto.stored.modular.archives.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.auth.core.util.StpLoginUserUtil;
import com.bto.common.listener.CommonDataChangeEventCenter;
import com.bto.stored.core.enums.StoredDataTypeEnum;
import com.bto.stored.modular.archives.entity.BtoPlantBase;
import com.bto.stored.modular.archives.mapper.BtoPlantBaseMapper;
import com.bto.stored.modular.archives.param.BtoPlantBaseAddParam;
import com.bto.stored.modular.archives.param.BtoPlantBaseEditParam;
import com.bto.stored.modular.archives.param.BtoPlantBaseIdParam;
import com.bto.stored.modular.archives.param.BtoPlantBasePageParam;
import com.bto.stored.modular.archives.service.BtoPlantBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.bto.common.enums.CommonSortOrderEnum;
import com.bto.common.exception.CommonException;
import com.bto.common.page.CommonPageRequest;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 电站档案Service接口实现类
 *
 * <AUTHOR>
 **/
@Service
public class BtoPlantBaseServiceImpl extends ServiceImpl<BtoPlantBaseMapper, BtoPlantBase> implements BtoPlantBaseService {

    @Override
    public Page<BtoPlantBase> page(BtoPlantBasePageParam btoPlantBasePageParam) {
        QueryWrapper<BtoPlantBase> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotEmpty(btoPlantBasePageParam.getPlantName())) {
            queryWrapper.lambda().like(BtoPlantBase::getPlantName, btoPlantBasePageParam.getPlantName());
        }
        if (ObjectUtil.isNotEmpty(btoPlantBasePageParam.getProvince())) {
            queryWrapper.lambda().eq(BtoPlantBase::getProvince, btoPlantBasePageParam.getProvince());
        }
        if (ObjectUtil.isNotEmpty(btoPlantBasePageParam.getCity())) {
            queryWrapper.lambda().eq(BtoPlantBase::getCity, btoPlantBasePageParam.getCity());
        }
        if (ObjectUtil.isNotEmpty(btoPlantBasePageParam.getArea())) {
            queryWrapper.lambda().eq(BtoPlantBase::getArea, btoPlantBasePageParam.getArea());
        }
        if (ObjectUtil.isNotEmpty(btoPlantBasePageParam.getTown())) {
            queryWrapper.lambda().eq(BtoPlantBase::getTown, btoPlantBasePageParam.getTown());
        }
        if (ObjectUtil.isNotEmpty(btoPlantBasePageParam.getAddress())) {
            queryWrapper.lambda().like(BtoPlantBase::getAddress, btoPlantBasePageParam.getAddress());
        }
        if (ObjectUtil.isAllNotEmpty(btoPlantBasePageParam.getSortField(), btoPlantBasePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(btoPlantBasePageParam.getSortOrder());
            queryWrapper.orderBy(true, btoPlantBasePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(btoPlantBasePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BtoPlantBase::getPlantUid);
        }
        // 校验数据范围
        List<String> loginUserProjectScope = StpLoginUserUtil.getLoginUserProjectScope();
        if (ObjectUtil.isNotEmpty(loginUserProjectScope)) {
            queryWrapper.lambda().in(BtoPlantBase::getProjectSpecial, loginUserProjectScope);
        } else {
            return new Page<>();
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BtoPlantBaseAddParam btoPlantBaseAddParam) {
        BtoPlantBase btoPlantBase = BeanUtil.toBean(btoPlantBaseAddParam, BtoPlantBase.class);
        String uuid = IdUtil.randomUUID().toUpperCase();
        btoPlantBase.setPlantUid("BTO-" + uuid);
        this.save(btoPlantBase);
        // 发布增加事件
        CommonDataChangeEventCenter.doAddWithData(StoredDataTypeEnum.ARCHIVERS.getValue(), JSONUtil.createArray().put(btoPlantBase));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BtoPlantBaseEditParam btoPlantBaseEditParam) {
        BtoPlantBase btoPlantBase = this.queryEntity(btoPlantBaseEditParam.getPlantUid());
        BeanUtil.copyProperties(btoPlantBaseEditParam, btoPlantBase);
        this.updateById(btoPlantBase);
        // 发布更新事件
        CommonDataChangeEventCenter.doUpdateWithData(StoredDataTypeEnum.ARCHIVERS.getValue(), JSONUtil.createArray().put(btoPlantBase));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BtoPlantBaseIdParam> btoPlantBaseIdParamList) {
        // 执行删除
        List<String> toDeleteProjectIdList = CollStreamUtil.toList(btoPlantBaseIdParamList, BtoPlantBaseIdParam::getPlantUid);
        this.removeByIds(toDeleteProjectIdList);

        // 发布删除事件
        CommonDataChangeEventCenter.doDeleteWithDataId(StoredDataTypeEnum.ARCHIVERS.getValue(), toDeleteProjectIdList);
    }

    @Override
    public BtoPlantBase detail(BtoPlantBaseIdParam btoPlantBaseIdParam) {
        return this.queryEntity(btoPlantBaseIdParam.getPlantUid());
    }

    @Override
    public BtoPlantBase queryEntity(String id) {
        BtoPlantBase btoPlantBase = this.getById(id);
        if (ObjectUtil.isEmpty(btoPlantBase)) {
            throw new CommonException("电站档案不存在，id值为：{}", id);
        }
        return btoPlantBase;
    }

    @Override
    public List<String> getPlantIdListByUserId(String userId) {
        QueryWrapper<BtoPlantBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(BtoPlantBase::getPlantUid);
        queryWrapper.lambda().eq(BtoPlantBase::getUserUid, userId);
        List<BtoPlantBase> list = this.list(queryWrapper);
        if (ObjectUtil.isEmpty(list)) {
            throw new CommonException("电站档案不存在，userId值为：{}", userId);
        }
        return list.stream().map(BtoPlantBase::getPlantUid).collect(Collectors.toList());
    }

    @Override
    public List<String> getPlantIdListByProjectList(List<String> projectList) {
        QueryWrapper<BtoPlantBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(BtoPlantBase::getProjectSpecial, projectList);
        List<BtoPlantBase> list = this.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            return list.stream().map(BtoPlantBase::getPlantUid).collect(Collectors.toList());
        }
        return null;
    }
}