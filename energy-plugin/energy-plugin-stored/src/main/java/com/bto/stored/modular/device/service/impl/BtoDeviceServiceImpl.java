package com.bto.stored.modular.device.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.auth.core.util.StpLoginUserUtil;
import com.bto.common.listener.CommonDataChangeEventCenter;
import com.bto.stored.core.enums.StoredDataTypeEnum;
import com.bto.stored.modular.archives.entity.BtoPlantBase;
import com.bto.stored.modular.archives.service.BtoPlantBaseService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.bto.common.enums.CommonSortOrderEnum;
import com.bto.common.exception.CommonException;
import com.bto.common.page.CommonPageRequest;
import com.bto.stored.modular.device.entity.BtoDevice;
import com.bto.stored.modular.device.mapper.BtoDeviceMapper;
import com.bto.stored.modular.device.param.BtoDeviceAddParam;
import com.bto.stored.modular.device.param.BtoDeviceEditParam;
import com.bto.stored.modular.device.param.BtoDeviceIdParam;
import com.bto.stored.modular.device.param.BtoDevicePageParam;
import com.bto.stored.modular.device.service.BtoDeviceService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 设备信息Service接口实现类
 *
 * <AUTHOR>
 **/
@Service
public class BtoDeviceServiceImpl extends ServiceImpl<BtoDeviceMapper, BtoDevice> implements BtoDeviceService {

    @Resource
    private BtoPlantBaseService plantBaseService;

    @Override
    public Page<BtoDevice> page(BtoDevicePageParam btoDevicePageParam) {
        QueryWrapper<BtoDevice> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isAllNotEmpty(btoDevicePageParam.getSortField(), btoDevicePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(btoDevicePageParam.getSortOrder());
            queryWrapper.orderBy(true, btoDevicePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(btoDevicePageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BtoDevice::getPlantUid);
        }
        // 校验数据范围
        List<String> loginUserProjectScope = StpLoginUserUtil.getLoginUserProjectScope();
        if (ObjectUtil.isNotEmpty(loginUserProjectScope)) {
            queryWrapper.lambda().in(BtoDevice::getProjectSpecial, loginUserProjectScope);
        } else {
            return new Page<>();
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BtoDeviceAddParam btoDeviceAddParam) {
        queryPlantBase(btoDeviceAddParam.getPlantUid());
        String deviceId = btoDeviceAddParam.getDeviceId();
        if (ObjectUtil.isNotEmpty(this.getById(deviceId))) {
            throw new CommonException("设备信息已存在，id值为：{}", deviceId);
        }
        BtoDevice btoDevice = BeanUtil.toBean(btoDeviceAddParam, BtoDevice.class);
        this.save(btoDevice);

        // 发布增加事件
        CommonDataChangeEventCenter.doAddWithData(StoredDataTypeEnum.DEVICE.getValue(), JSONUtil.createArray().put(btoDevice));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BtoDeviceEditParam btoDeviceEditParam) {
        queryPlantBase(btoDeviceEditParam.getPlantUid());
        BtoDevice btoDevice = this.queryEntity(btoDeviceEditParam.getDeviceId());
        BeanUtil.copyProperties(btoDeviceEditParam, btoDevice);
        this.updateById(btoDevice);

        // 发布更新事件
        CommonDataChangeEventCenter.doUpdateWithData(StoredDataTypeEnum.DEVICE.getValue(), JSONUtil.createArray().put(btoDevice));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BtoDeviceIdParam> btoDeviceIdParamList) {
        // 执行删除
        List<String> toDeleteDeviceIdList = CollStreamUtil.toList(btoDeviceIdParamList, BtoDeviceIdParam::getDeviceId);
        this.removeByIds(toDeleteDeviceIdList);

        // 发布删除事件
        CommonDataChangeEventCenter.doDeleteWithDataId(StoredDataTypeEnum.DEVICE.getValue(), toDeleteDeviceIdList);
    }

    @Override
    public BtoDevice detail(BtoDeviceIdParam btoDeviceIdParam) {
        return this.queryEntity(btoDeviceIdParam.getDeviceId());
    }

    @Override
    public BtoDevice queryEntity(String id) {
        BtoDevice btoDevice = this.getById(id);
        if (ObjectUtil.isEmpty(btoDevice)) {
            throw new CommonException("设备信息不存在，id值为：{}", id);
        }
        return btoDevice;
    }

    @Override
    public List<BtoDevice> getDevicesByPlantId(String plantId) {
        LambdaQueryWrapper<BtoDevice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BtoDevice::getPlantUid, plantId);
        return this.list(queryWrapper);
    }

    public void queryPlantBase(String id) {
        BtoPlantBase btoPlantBase = plantBaseService.queryEntity(id);
        if (ObjectUtil.isEmpty(btoPlantBase)) {
            throw new CommonException("电站档案信息不存在，id值为：{}", id);
        }
    }
}