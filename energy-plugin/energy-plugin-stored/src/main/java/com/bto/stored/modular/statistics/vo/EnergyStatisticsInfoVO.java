package com.bto.stored.modular.statistics.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * <AUTHOR> by zhb on 2024/8/24.
 */
@Getter
@Setter
public class EnergyStatisticsInfoVO {

    /** 昨日收益 */
    @ApiModelProperty(value = "昨日收益", position = 1)
    private Double yesterdayRevenue;

    /** 昨日费用 */
    @ApiModelProperty(value = "昨日费用", position = 2)
    private Double yesterdayCost;

    /** 总收益 */
    @ApiModelProperty(value = "总收益", position = 3)
    private Double totalRevenue;

    /** 总费用 */
    @ApiModelProperty(value = "总费用", position = 4)
    private Double totalCost;

    /** 今日发电量 */
    @ApiModelProperty(value = "今日发电量（单位kwh）", position = 5)
    private Double todayGeneratedPower;

    /** 总发电量 */
    @ApiModelProperty(value = "总发电量（单位kwh）", position = 6)
    private Double totalGeneratedPower;

    /** 今日用电量 */
    @ApiModelProperty(value = "今日用电量（单位kwh）", position = 7)
    private Double todayPowerUsage;

    /** 总用电量 */
    @ApiModelProperty(value = "总用电量（单位kwh）", position = 8)
    private Double totalPowerUsage;

    /** 今日买电量 */
    @ApiModelProperty(value = "今日买电量（单位kwh）", position = 9)
    private Double todayPurchasedPower;

    /** 总买电量 */
    @ApiModelProperty(value = "总买电量（单位kwh）", position = 10)
    private Double totalPurchasedPower;

    /** 今日卖电量 */
    @ApiModelProperty(value = "今日卖电量（单位kwh）", position = 11)
    private Double todaySoldPower;

    /** 总卖电量 */
    @ApiModelProperty(value = "总卖电量（单位kwh）", position = 12)
    private Double totalSoldPower;

    /** 今日充电量 */
    @ApiModelProperty(value = "今日充电量（单位kwh）", position = 13)
    private Double todayChargedPower;

    /** 总充电量 */
    @ApiModelProperty(value = "总充电量（单位kwh）", position = 14)
    private Double totalChargedPower;

    /** 今日放电量 */
    @ApiModelProperty(value = "今日放电量（单位kwh）", position = 15)
    private Double todayDischargedPower;

    /** 总放电量 */
    @ApiModelProperty(value = "总放电量（单位kwh）", position = 16)
    private Double totalDischargedPower;

    /** 更新时间 */
    @ApiModelProperty(value = "更新时间", position = 17)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // 设置测试数据
    public void setTestData() {
        // 昨日收益和费用
        setYesterdayRevenue(20.0);
        setYesterdayCost(36.09);

        // 总收益和费用
        setTotalRevenue(676.42);
        setTotalCost(391.4);

        // 今日发电量、用电量、买电量、卖电量、充电量、放电量（单位假设为kWh，如果总发电量等是MWh，则需要相应转换）
        setTodayGeneratedPower(18.65);
        setTodayPowerUsage(59.12);
        setTodayPurchasedPower(665.12);
        setTodaySoldPower(0.0);
        setTodayChargedPower(156.79);
        setTodayDischargedPower(87.45);

        // 总发电量、用电量、买电量、卖电量、充电量、放电量（注意单位，这里假设totalGeneratedPower已经是MWh，其他根据需要调整）
        setTotalGeneratedPower(26.04 * 1000.0); // 转换为kWh，如果保持为MWh则不需要乘1000
        setTotalPowerUsage(1.49 * 1000.0);
        setTotalPurchasedPower(26.09 * 1000.0);
        setTotalSoldPower(45.62 * 1000.0);
        setTotalChargedPower(27.17 * 1000.0);
        setTotalDischargedPower(25.68 * 1000.0);

        // 注意：如果totalGeneratedPower等字段的单位是MWh，并且您希望保持这种单位，
        // 那么在设置时就不需要乘以1000。但是，在显示给用户或进行某些计算时，
        // 您可能需要将MWh转换为kWh（或反之）。
    }

}