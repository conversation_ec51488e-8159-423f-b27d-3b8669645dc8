package com.bto.stored.modular.device.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 设备信息实体
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@TableName("bto_device")
public class BtoDevice {

    /** 电站uid */
    @ApiModelProperty(value = "电站uid", position = 1)
    private String plantUid;

    /** 设备编号 */
    @TableId
    @ApiModelProperty(value = "设备编号", position = 2)
    private String deviceId;

    /** 运维器通讯模块imei*/
    @ApiModelProperty(value = "运维器通讯模块imei", position = 3)
    private String imei;

    /** 设备类型（
     1：逆变器，
     2：运维器，
     3：电表（全部数据），
     31：电表（卖电买电数据），
     4：气象站，
     5：储能机，
     6：开关，
     11：配电柜，
     12：温湿度、烟感采集器
     ） */
    @ApiModelProperty(value = "设备类型（1：逆变器，2：运维器，3：电表（全部数据），31：电表（卖电买电数据），4：气象站，5：储能机，6：开关，11：配电柜，12：温湿度、烟感采集器）", position = 3)
    private Integer deviceType;

    /** 厂家 */
    @ApiModelProperty(value = "厂家", position = 5)
    private String manufacturer;

    /** 型号 */
    @ApiModelProperty(value = "型号", position = 6)
    private String module;

    /** 项目专项 */
    @ApiModelProperty(value = "项目专项", position = 7)
    private Long projectSpecial;

    /** 设备地址 */
    @ApiModelProperty(value = "设备地址", position = 8)
    private String deviceAddress;

    /** 物联网卡号 */
    @ApiModelProperty(value = "物联网卡号", position = 11)
    private String iccid;

    /** 状态（0：离线，1：正常运行，2：告警运行，3：自检提示，4：夜间离线） */
    @ApiModelProperty(value = "状态（0：离线，1：正常运行，2：告警运行，3：自检提示，4：夜间离线）", position = 12)
    private Integer status;

    /** 设备开关机状态（0：关机，1：启动） */
    @ApiModelProperty(value = "设备开关机状态（0：关机，1：启动）", position = 13)
    private String enable;

    /** 运维器是否采集配电箱数据（1：是，0：否） */
    @ApiModelProperty(value = "运维器是否采集配电箱数据（1：是，0：否）", position = 14)
    private String cluster;

    /** 安装地址 */
    @ApiModelProperty(value = "安装地址", position = 15)
    private String installAddress;

    /** 激活时间 */
    @ApiModelProperty(value = "激活时间", position = 16)
    private Date startTime;

    /** 质保时间 */
    @ApiModelProperty(value = "质保时间", position = 17)
    private Date endTime;

    /** 物联卡状态 */
    @ApiModelProperty(value = "物联卡状态", position = 18)
    private Integer cardStatus;

    /** 数据创建时间 */
    @ApiModelProperty(value = "数据创建时间", position = 24)
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 数据更新时间 */
    @ApiModelProperty(value = "数据更新时间", position = 25)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

    /** 逆变器接入类型（0：并网，1：储能） */
    @ApiModelProperty(value = "逆变器接入类型（0：并网，1：储能）", position = 26)
    private String receiveType;
}