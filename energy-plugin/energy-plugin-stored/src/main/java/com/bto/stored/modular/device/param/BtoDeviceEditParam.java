package com.bto.stored.modular.device.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 设备信息编辑参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class BtoDeviceEditParam {

    /** 电站uid */
    @ApiModelProperty(value = "电站uid", required = true, position = 1)
    @NotBlank(message = "plantUid不能为空")
    private String plantUid;

    /** 设备编号 */
    @ApiModelProperty(value = "设备编号", position = 2)
    @NotBlank(message = "deviceId不能为空")
    private String deviceId;

    /** 运维器通讯模块imei*/
    @ApiModelProperty(value = "运维器通讯模块imei", position = 3)
    private String imei;

    /** 设备类型（
     1：逆变器，
     2：运维器，
     3：电表（全部数据），
     31：电表（卖电买电数据），
     4：气象站，
     5：储能机，
     6：开关，
     11：配电柜，
     12：温湿度、烟感采集器
     ） */
    @ApiModelProperty(value = "设备类型（1：逆变器，2：运维器，3：电表（全部数据），31：电表（卖电买电数据），4：气象站，5：储能机，6：开关，11：配电柜，12：温湿度、烟感采集器）", position = 3)
    @NotNull(message = "deviceType不能为空")
    private Integer deviceType;

    /** 厂家 */
    @ApiModelProperty(value = "厂家", position = 5)
    private String manufacturer;

    /** 型号 */
    @ApiModelProperty(value = "型号", position = 6)
    private String module;

    /** 项目专项 */
    @ApiModelProperty(value = "项目专项", position = 7)
    private Long projectSpecial;

    /** 设备地址（逻辑地址） */
    @ApiModelProperty(value = "设备地址", position = 8)
    private String deviceAddress;

    /** 物联网卡号 */
    @ApiModelProperty(value = "物联网卡号", position = 11)
    private String iccid;

    /** 安装地址 */
    @ApiModelProperty(value = "安装地址", position = 15)
    private String installAddress;

    /** 到期时间/质保时间 */
    @ApiModelProperty(value = "到期时间/质保时间", position = 17)
    private Date endTime;

    /** 逆变器接入类型（0：并网，1：储能） */
    @ApiModelProperty(value = "逆变器接入类型（0：并网，1：储能）", position = 26)
    @NotBlank(message = "receiveType不能为空")
    private String receiveType;

}