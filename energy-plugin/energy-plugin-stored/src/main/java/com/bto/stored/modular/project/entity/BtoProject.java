package com.bto.stored.modular.project.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import com.bto.common.pojo.CommonEntity;

/**
 * 组织实体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("bto_project")
public class BtoProject extends CommonEntity {

    /** id */
    @ApiModelProperty(value = "id", position = 1)
    private String id;

    /** 父id */
    @ApiModelProperty(value = "父id", position = 2)
    private String parentId;

    /** 标题 */
    @ApiModelProperty(value = "标题", position = 3)
    private String title;

    /** 项目名称 */
    @ApiModelProperty(value = "项目名称", position = 4)
    private String name;

    /** 系统LOGO */
    @ApiModelProperty(value = "系统LOGO", position = 5)
    private String sysLogo;

    /** 大屏LOGO */
    @ApiModelProperty(value = "大屏LOGO", position = 6)
    private String screenLogo;

    /** 排序码 */
    @ApiModelProperty(value = "排序码", position = 7)
    private Integer sortCode;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 8)
    @TableField(insertStrategy = FieldStrategy.IGNORED, updateStrategy = FieldStrategy.IGNORED)
    private String extJson;

}