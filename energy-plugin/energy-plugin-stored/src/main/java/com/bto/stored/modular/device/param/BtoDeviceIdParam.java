package com.bto.stored.modular.device.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 设备信息Id参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class BtoDeviceIdParam {

    /** 设备编号 */
    @ApiModelProperty(value = "设备编号", position = 2)
    @NotBlank(message = "deviceId不能为空")
    private String deviceId;
}