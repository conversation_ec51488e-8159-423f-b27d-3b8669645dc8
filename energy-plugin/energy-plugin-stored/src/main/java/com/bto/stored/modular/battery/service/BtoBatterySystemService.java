package com.bto.stored.modular.battery.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.stored.modular.battery.entity.BtoBatterySystem;
import com.bto.stored.modular.battery.param.BtoBatterySystemIdParam;
import com.bto.stored.modular.battery.param.BtoBatterySystemPageParam;

import java.util.List;

/**
 * 储能（电池）系统数据Service接口
 *
 * <AUTHOR>
 **/
public interface BtoBatterySystemService extends IService<BtoBatterySystem> {

    Page<BtoBatterySystem> page(BtoBatterySystemPageParam btoBatterySystemPageParam);

    List<BtoBatterySystem> detail(BtoBatterySystemIdParam btoBatterySystemIdParam);

    /**
     * 获取储能（电池）系统数据分页
     *
     * <AUTHOR>
     **/
    void queryEntity(String inverterSn);
}