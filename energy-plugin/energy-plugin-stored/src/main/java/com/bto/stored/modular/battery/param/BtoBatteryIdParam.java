package com.bto.stored.modular.battery.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * 电池数据Id参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class BtoBatteryIdParam {

    /** 逆变器SN */
    @NotBlank(message = "逆变器SN不能为空")
    @ApiModelProperty(value = "逆变器SN", required = true)
    private String inverterSn;

    /** 开始时间（年月日） */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间", required = true)
    private LocalDate startDate;

    /** 结束时间（年月日） */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间", required = true)
    private LocalDate endDate;

}