package com.bto.stored.modular.project.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bto.common.listener.CommonDataChangeEventCenter;
import com.bto.stored.core.enums.StoredDataTypeEnum;
import com.bto.stored.modular.archives.entity.BtoPlantBase;
import com.bto.stored.modular.archives.service.BtoPlantBaseService;
import com.bto.stored.modular.project.entity.BtoProject;
import com.bto.stored.modular.project.mapper.BtoProjectMapper;
import com.bto.stored.modular.project.param.*;
import com.bto.stored.modular.project.service.BtoProjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.bto.common.cache.CommonCacheOperator;
import com.bto.common.enums.CommonSortOrderEnum;
import com.bto.common.exception.CommonException;
import com.bto.common.page.CommonPageRequest;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目Service接口实现类
 *
 * <AUTHOR>
 */
@Service
public class BtoProjectServiceImpl extends ServiceImpl<BtoProjectMapper, BtoProject> implements BtoProjectService {

    @Resource
    private CommonCacheOperator commonCacheOperator;

    @Resource
    private BtoPlantBaseService plantBaseService;

    @Override
    public Page<BtoProject> page(BtoProjectPageParam btoProjectPageParam) {
        QueryWrapper<BtoProject> queryWrapper = new QueryWrapper<>();
        // 查询部分字段
        queryWrapper.lambda().select(BtoProject::getId, BtoProject::getParentId, BtoProject::getName,
                BtoProject::getTitle, BtoProject::getSysLogo, BtoProject::getScreenLogo, BtoProject::getSortCode);
        if (ObjectUtil.isNotEmpty(btoProjectPageParam.getParentId())) {
            queryWrapper.lambda().eq(BtoProject::getParentId, btoProjectPageParam.getParentId());
        }
        if (ObjectUtil.isNotEmpty(btoProjectPageParam.getSearchKey())) {
            queryWrapper.lambda().like(BtoProject::getName, btoProjectPageParam.getSearchKey());
        }
        if (ObjectUtil.isAllNotEmpty(btoProjectPageParam.getSortField(), btoProjectPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(btoProjectPageParam.getSortOrder());
            queryWrapper.orderBy(true, btoProjectPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(btoProjectPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BtoProject::getSortCode);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<Tree<String>> tree() {
        List<BtoProject> btoProjectList = this.getAllProjectList();
        List<TreeNode<String>> treeNodeList = btoProjectList.stream().map(project ->
                        new TreeNode<>(project.getId(), project.getParentId(),
                                project.getName(), project.getSortCode()).setExtra(JSONUtil.parseObj(project)))
                .collect(Collectors.toList());
        return TreeUtil.build(treeNodeList, "0");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(BtoProjectAddParam btoProjectAddParam) {
        BtoProject btoProject = BeanUtil.toBean(btoProjectAddParam, BtoProject.class);
        // 重复名称
        boolean repeatName = this.count(new LambdaQueryWrapper<BtoProject>().eq(BtoProject::getParentId, btoProject.getParentId())
                .eq(BtoProject::getName, btoProject.getName())) > 0;
        if (repeatName) {
            throw new CommonException("存在重复的同级项目，名称为：{}", btoProject.getName());
        }
        this.save(btoProject);

        // 发布增加事件
        CommonDataChangeEventCenter.doAddWithData(StoredDataTypeEnum.PROJECT.getValue(), JSONUtil.createArray().put(btoProject));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(BtoProjectEditParam btoProjectEditParam) {
        BtoProject project = this.queryEntity(btoProjectEditParam.getId());
        BeanUtil.copyProperties(btoProjectEditParam, project);
        boolean repeatName = this.count(new LambdaQueryWrapper<BtoProject>().eq(BtoProject::getParentId, project.getParentId())
                .eq(BtoProject::getName, project.getName()).ne(BtoProject::getId, project.getId())) > 0;
        if (repeatName) {
            throw new CommonException("存在重复的同级项目，名称为：{}", project.getName());
        }
        List<BtoProject> originDataList = this.getAllProjectList();
        boolean errorLevel = this.getChildListById(originDataList, project.getId(), true).stream()
                .map(BtoProject::getId).collect(Collectors.toList()).contains(project.getParentId());
        if (errorLevel) {
            throw new CommonException("不可选择上级项目：{}", this.getById(originDataList, project.getParentId()).getName());
        }
        this.updateById(project);

        // 发布更新事件
        CommonDataChangeEventCenter.doUpdateWithData(StoredDataTypeEnum.PROJECT.getValue(), JSONUtil.createArray().put(project));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<BtoProjectIdParam> btoProjectIdParams) {
        List<String> projectIdList = CollStreamUtil.toList(btoProjectIdParams, BtoProjectIdParam::getId);
        if (ObjectUtil.isNotEmpty(projectIdList)) {
            List<BtoProject> allProjectList = this.getAllProjectList();
            // 获取所有子项目
            List<String> toDeleteProjectIdList = CollectionUtil.newArrayList();

            projectIdList.forEach(projectId -> toDeleteProjectIdList.addAll(this.getChildListById(allProjectList, projectId, true).stream()
                    .map(BtoProject::getId).collect(Collectors.toList())));

            // 项目下有电站不能删除
            boolean hasProjectPlant = plantBaseService.count(new LambdaQueryWrapper<BtoPlantBase>().in(BtoPlantBase::getProjectSpecial, toDeleteProjectIdList)) > 0;
            if (hasProjectPlant) {
                throw new CommonException("请先删除项目下的电站");
            }
            // 执行删除
            this.removeByIds(toDeleteProjectIdList);

            // 发布删除事件
            CommonDataChangeEventCenter.doDeleteWithDataId(StoredDataTypeEnum.PROJECT.getValue(), toDeleteProjectIdList);
        }
    }

    @Override
    public BtoProject detail(BtoProjectIdParam btoProjectIdParam) {
        return this.queryEntity(btoProjectIdParam.getId());
    }

    @Override
    public BtoProject queryEntity(String id) {
        BtoProject project = this.getById(id);
        if (ObjectUtil.isEmpty(project)) {
            throw new CommonException("项目不存在，id值为：{}", id);
        }
        return project;
    }

    @Override
    public List<BtoProject> getAllProjectList() {
        List<BtoProject> projectList = this.list(new LambdaQueryWrapper<BtoProject>().orderByAsc(BtoProject::getSortCode));
        if (ObjectUtil.isNotEmpty(projectList)) {
            return projectList;
        }
        throw new CommonException("项目数据为空");
    }

    @Override
    public String getProjectIdByProjectFullNameWithCreate(String projectFullName) {
        List<BtoProject> cachedAllProjectList = this.getAllProjectList();
        List<Tree<String>> treeList = TreeUtil.build(cachedAllProjectList.stream().map(project ->
                        new TreeNode<>(project.getId(), project.getParentId(), project.getName(), project.getSortCode()))
                .collect(Collectors.toList()), "0");
        return findProjectIdByProjectName("0", StrUtil.split(projectFullName, StrUtil.DASHED).iterator(), cachedAllProjectList, treeList);
    }

    public String findProjectIdByProjectName(String parentId, Iterator<String> iterator, List<BtoProject> cachedAllProjectList, List<Tree<String>> treeList) {
        String projectName = iterator.next();
        if (ObjectUtil.isNotEmpty(treeList)) {
            List<Tree<String>> findList = treeList.stream().filter(tree -> tree.getName().equals(projectName)).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(findList)) {
                if (iterator.hasNext()) {
                    return findProjectIdByProjectName(findList.get(0).getId(), iterator, cachedAllProjectList, findList.get(0).getChildren());
                } else {
                    return findList.get(0).getId();
                }
            }
        }
        String projectId = this.doCreateProject(parentId, projectName, cachedAllProjectList);
        if (iterator.hasNext()) {
            return findProjectIdByProjectName(projectId, iterator, cachedAllProjectList, CollectionUtil.newArrayList());
        } else {
            return projectId;
        }
    }

    /**
     * 执行创建项目
     *
     **/
    public String doCreateProject(String parentId, String projectName, List<BtoProject> cachedAllProjectList) {
        // 创建该项目
        BtoProject project = new BtoProject();
        project.setName(projectName);
        project.setParentId(parentId);
        project.setSortCode(99);
        this.save(project);
        // 发布增加事件
        CommonDataChangeEventCenter.doAddWithData(StoredDataTypeEnum.PROJECT.getValue(), JSONUtil.createArray().put(project));
        return project.getId();
    }

    /* ====项目部分所需要用到的选择器==== */

    @Override
    public List<Tree<String>> projectTreeSelector() {
        List<BtoProject> btoProjectList = this.getAllProjectList();
        List<TreeNode<String>> treeNodeList = btoProjectList.stream().map(project ->
                        new TreeNode<>(project.getId(), project.getParentId(), project.getName(), project.getSortCode()))
                .collect(Collectors.toList());
        return TreeUtil.build(treeNodeList, "0");
    }

    @Override
    public Page<BtoProject> projectListSelector(BtoProjectSelectorProjectListParam btoProjectSelectorProjectListParam) {
        LambdaQueryWrapper<BtoProject> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 查询部分字段
        lambdaQueryWrapper.select(BtoProject::getId, BtoProject::getParentId, BtoProject::getName,
                BtoProject::getTitle, BtoProject::getSysLogo, BtoProject::getScreenLogo, BtoProject::getSortCode);
        if (ObjectUtil.isNotEmpty(btoProjectSelectorProjectListParam.getParentId())) {
            lambdaQueryWrapper.eq(BtoProject::getParentId, btoProjectSelectorProjectListParam.getParentId());
        }
        if (ObjectUtil.isNotEmpty(btoProjectSelectorProjectListParam.getSearchKey())) {
            lambdaQueryWrapper.like(BtoProject::getName, btoProjectSelectorProjectListParam.getSearchKey());
        }
        lambdaQueryWrapper.orderByAsc(BtoProject::getSortCode);
        return this.page(CommonPageRequest.defaultPage(), lambdaQueryWrapper);
    }


    /* ====以下为各种递归方法==== */

    @Override
    public List<BtoProject> getParentAndChildListById(List<BtoProject> originDataList, String id, boolean includeSelf) {
        List<BtoProject> parentListById = this.getParentListById(originDataList, id, false);
        List<BtoProject> childListById = this.getChildListById(originDataList, id, true);
        parentListById.addAll(childListById);
        return parentListById;
    }

    @Override
    public List<BtoProject> getChildListById(List<BtoProject> originDataList, String id, boolean includeSelf) {
        List<BtoProject> resultList = CollectionUtil.newArrayList();
        execRecursionFindChild(originDataList, id, resultList);
        if (includeSelf) {
            BtoProject self = this.getById(originDataList, id);
            if (ObjectUtil.isNotEmpty(self)) {
                resultList.add(self);
            }
        }
        return resultList;
    }

    @Override
    public List<BtoProject> getParentListById(List<BtoProject> originDataList, String id, boolean includeSelf) {
        List<BtoProject> resultList = CollectionUtil.newArrayList();
        execRecursionFindParent(originDataList, id, resultList);
        if (includeSelf) {
            BtoProject self = this.getById(originDataList, id);
            if (ObjectUtil.isNotEmpty(self)) {
                resultList.add(self);
            }
        }
        return resultList;
    }

    public void execRecursionFindChild(List<BtoProject> originDataList, String id, List<BtoProject> resultList) {
        originDataList.forEach(item -> {
            if (item.getParentId().equals(id)) {
                resultList.add(item);
                execRecursionFindChild(originDataList, item.getId(), resultList);
            }
        });
    }

    public void execRecursionFindParent(List<BtoProject> originDataList, String id, List<BtoProject> resultList) {
        originDataList.forEach(item -> {
            if (item.getId().equals(id)) {
                BtoProject parent = this.getById(originDataList, item.getParentId());
                if (ObjectUtil.isNotEmpty(parent)) {
                    resultList.add(parent);
                }
                execRecursionFindParent(originDataList, item.getParentId(), resultList);
            }
        });
    }

    @Override
    public BtoProject getById(List<BtoProject> originDataList, String id) {
        int index = CollStreamUtil.toList(originDataList, BtoProject::getId).indexOf(id);
        return index == -1 ? null : originDataList.get(index);
    }

    @Override
    public BtoProject getParentById(List<BtoProject> originDataList, String id) {
        BtoProject self = this.getById(originDataList, id);
        return ObjectUtil.isNotEmpty(self) ? self : this.getById(originDataList, self.getParentId());
    }

    @Override
    public BtoProject getChildById(List<BtoProject> originDataList, String id) {
        int index = CollStreamUtil.toList(originDataList, BtoProject::getParentId).indexOf(id);
        return index == -1 ? null : originDataList.get(index);
    }
}