package com.bto.stored.modular.inverter.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.bto.common.exception.CommonException;
import com.bto.common.page.CommonPageRequest;
import com.bto.stored.modular.inverter.entity.BtoInverter;
import com.bto.stored.modular.inverter.mapper.BtoInverterMapper;
import com.bto.stored.modular.inverter.param.BtoInverterIdParam;
import com.bto.stored.modular.inverter.param.BtoInverterPageParam;
import com.bto.stored.modular.inverter.service.BtoInverterService;

import javax.annotation.Resource;
import java.util.List;

/**
 * 逆变器实时数据数据Service接口实现类
 *
 * <AUTHOR>
 **/
@Service
public class BtoInverterServiceImpl extends ServiceImpl<BtoInverterMapper, BtoInverter> implements BtoInverterService {

    @Resource
    private BtoInverterMapper btoInverterMapper;

    @Override
    public Page<BtoInverter> page(BtoInverterPageParam btoInverterPageParam) {
        String tableName = "bto_inverter_";
        return btoInverterMapper.selectPageWithDynamicTableName(CommonPageRequest.defaultPage(), btoInverterPageParam, tableName);
    }

    @Override
    public List<BtoInverter> detail(BtoInverterIdParam btoInverterIdParam) {
        QueryWrapper<BtoInverter> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("inverter_sn", btoInverterIdParam.getInverterSn());
        if (ObjectUtil.isNotEmpty(btoInverterIdParam.getStartDate()) && ObjectUtil.isNotEmpty(btoInverterIdParam.getEndDate())) {
            queryWrapper.between("DATE(init_time)", btoInverterIdParam.getStartDate().toString(), btoInverterIdParam.getEndDate().toString());
        }
        List<BtoInverter> list = this.list(queryWrapper);
        if (ObjectUtil.isEmpty(list)) {
            throw new CommonException("在指定时间范围内未找到逆变器数据，InverterSn值为：{}", btoInverterIdParam.getInverterSn());
        }
        return list;
    }

}