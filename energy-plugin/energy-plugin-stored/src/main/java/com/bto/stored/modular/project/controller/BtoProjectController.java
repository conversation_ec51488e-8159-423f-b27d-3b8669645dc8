package com.bto.stored.modular.project.controller;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.stored.modular.project.entity.BtoProject;
import com.bto.stored.modular.project.param.*;
import com.bto.stored.modular.project.service.BtoProjectService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.bto.common.annotation.CommonLog;
import com.bto.common.pojo.CommonResult;
import com.bto.common.pojo.CommonValidList;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 项目类型控制器
 * <AUTHOR>
 */
@Api(tags = "项目类型控制器")
@ApiSupport(author = "BTO", order = 5)
@RestController
@Validated
public class BtoProjectController {

    @Resource
    private BtoProjectService projectService;

    /**
     * 获取项目分页
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取项目分页")
    @GetMapping("/stored/project/page")
    public CommonResult<Page<BtoProject>> page(BtoProjectPageParam btoProjectPageParam) {
        return CommonResult.data(projectService.page(btoProjectPageParam));
    }

    /**
     * 获取项目树
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("获取项目树")
    @GetMapping("/stored/project/tree")
    public CommonResult<List<Tree<String>>> tree() {
        return CommonResult.data(projectService.tree());
    }

    /**
     * 添加项目
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("添加项目")
    @CommonLog("添加项目")
    @PostMapping("/stored/project/add")
    public CommonResult<String> add(@RequestBody @Valid BtoProjectAddParam btoProjectAddParam) {
        projectService.add(btoProjectAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑项目
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("编辑项目")
    @CommonLog("编辑项目")
    @PostMapping("/stored/project/edit")
    public CommonResult<String> edit(@RequestBody @Valid BtoProjectEditParam btoProjectEditParam) {
        projectService.edit(btoProjectEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除项目
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("删除项目")
    @CommonLog("删除项目")
    @PostMapping("/stored/project/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                       CommonValidList<BtoProjectIdParam> btoProjectIdParamList) {
        projectService.delete(btoProjectIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取项目详情
     */
    @ApiOperationSupport(order = 6)
    @ApiOperation("获取项目详情")
    @GetMapping("/stored/project/detail")
    public CommonResult<BtoProject> detail(@Valid BtoProjectIdParam btoProjectIdParam) {
        return CommonResult.data(projectService.detail(btoProjectIdParam));
    }

    /**
     * 获取项目树选择器
     */
    @ApiOperationSupport(order = 7)
    @ApiOperation("获取项目树选择器")
    @GetMapping("/stored/project/orgTreeSelector")
    public CommonResult<List<Tree<String>>> orgTreeSelector() {
        return CommonResult.data(projectService.projectTreeSelector());
    }
}