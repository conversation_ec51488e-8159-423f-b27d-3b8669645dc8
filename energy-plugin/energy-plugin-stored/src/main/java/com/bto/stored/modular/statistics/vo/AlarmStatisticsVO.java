package com.bto.stored.modular.statistics.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Random;

/**
 * <AUTHOR> by 2022 on 2024/8/24.
 */
@Getter
@Setter
public class AlarmStatisticsVO {

    /** 正常运行率 */
    @ApiModelProperty(value = "正常运行率", position = 1)
    private double normalRunningRate;

    /** 今日告警数量 */
    @ApiModelProperty(value = "当天告警数量", position = 2)
    private int todayAlarmCount;

    /** 离线数量 */
    @ApiModelProperty(value = "离线数量", position = 3)
    private int offlineCount;

    /** 待处理数量 */
    @ApiModelProperty(value = "待处理数量", position = 4)
    private int unprocessedCount;


    /** 紧急数量 */
    @ApiModelProperty(value = "紧急数量", position = 5)
    private int emergencyCount;

    /** 严重数量 */
    @ApiModelProperty(value = "严重数量", position = 6)
    private int seriousCount;

    /** 一般数量 */
    @ApiModelProperty(value = "一般数量", position = 7)
    private int generalCount;

    public void setTestData() {
        setTodayAlarmCount(getRandomInt(50, 100));
        setOfflineCount(getRandomInt(5, 20));
        setUnprocessedCount(getRandomInt(20, 40));
        setEmergencyCount(getRandomInt(1, 5));
        setSeriousCount(getRandomInt(1, 5));
        setGeneralCount(getUnprocessedCount() - getEmergencyCount() - getSeriousCount());
        setNormalRunningRate((100 - getUnprocessedCount()) / 100.0);
    }

    // 辅助方法：生成指定范围内的随机整数
    private int getRandomInt(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }
}