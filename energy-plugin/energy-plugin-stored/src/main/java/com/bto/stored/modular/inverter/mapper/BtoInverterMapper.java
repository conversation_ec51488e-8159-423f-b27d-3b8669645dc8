package com.bto.stored.modular.inverter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.stored.modular.inverter.entity.BtoInverter;
import com.bto.stored.modular.inverter.param.BtoInverterPageParam;
import org.apache.ibatis.annotations.Param;

/**
 * 逆变器实时数据数据Mapper接口
 *
 * <AUTHOR>
 **/
public interface BtoInverterMapper extends BaseMapper<BtoInverter> {
    Page<BtoInverter> selectPageWithDynamicTableName(Page<BtoInverter> page, @Param("param") BtoInverterPageParam param, @Param("tableName") String tableName);
}