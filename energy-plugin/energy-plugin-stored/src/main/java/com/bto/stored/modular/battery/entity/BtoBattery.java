package com.bto.stored.modular.battery.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 电池数据实体
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@TableName("bto_battery")
public class BtoBattery {

    /** ID */
    @TableId
    @ApiModelProperty(value = "ID", position = 1)
    private Long id;

    /** 逆变器SN */
    @ApiModelProperty(value = "逆变器SN", position = 2)
    private String inverterSn;

    /** 数据时间 */
    @ApiModelProperty(value = "数据时间", position = 3)
    private Date collectionTime;

    /** 逆变器工作模式（0：初始化，1：等待，2、运行3、离网4、电网有负荷模式、储能5、故障6、升级7、调试8、自检9、重置） */
    @ApiModelProperty(value = "逆变器工作模式（0：初始化，1：等待，2、运行3、离网4、电网有负荷模式、储能5、故障6、升级7、调试8、自检9、重置）", position = 4)
    private Integer mpvMode;

    /** 蓄电池能量流的方向（电池使用情况）|| 1:电池放电 0:没有电池能量流 -1：电池充电 */
    @ApiModelProperty(value = "蓄电池能量流的方向（电池使用情况）|| 1:电池放电 0:没有电池能量流 -1：电池充电", position = 5)
    private Integer batteryDirection;

    /** 电池总功率（W） */
    @ApiModelProperty(value = "电池总功率（W）", position = 6)
    private Integer totalBatteryPower;

    /** 日电池充电（kWh）*100 */
    @ApiModelProperty(value = "日电池充电（kWh）*100", position = 7)
    private Integer todayBatchgenergy;

    /** 月电池充电（kWh）*100 */
    @ApiModelProperty(value = "月电池充电（kWh）*100", position = 8)
    private Integer monthBatchgenergy;

    /** 年电池充电（kWh）*100 */
    @ApiModelProperty(value = "年电池充电（kWh）*100", position = 9)
    private Integer yearBatchgenergy;

    /** 总电池充电（kWh）*100 */
    @ApiModelProperty(value = "总电池充电（kWh）*100", position = 10)
    private Integer totalBatchgenergy;

    /** 日电池放电量（kWh）*100 */
    @ApiModelProperty(value = "日电池放电量（kWh）*100", position = 11)
    private Integer todayBatdisenergy;

    /** 月电池放电量（kWh）*100 */
    @ApiModelProperty(value = "月电池放电量（kWh）*100", position = 12)
    private Integer monthBatdisenergy;

    /** 年电池放电量（kWh）*100 */
    @ApiModelProperty(value = "年电池放电量（kWh）*100", position = 13)
    private Integer yearBatdisenergy;

    /** 总电池放电量（kWh）*100 */
    @ApiModelProperty(value = "总电池放电量（kWh）*100", position = 14)
    private Integer totalBatdisenergy;

    /** 电池1充电（kWh）*100 */
    @ApiModelProperty(value = "电池1充电（kWh）*100", position = 15)
    private Integer todayBat1Chgenergy;

    /** 电池1充电（kWh）*100 */
    @ApiModelProperty(value = "电池1充电（kWh）*100", position = 16)
    private Integer monthBat1Chgenergy;

    /** 电池1充电（kWh）*100 */
    @ApiModelProperty(value = "电池1充电（kWh）*100", position = 17)
    private Integer yearBat1Chgenergy;

    /** 电池1充电（kWh）*100 */
    @ApiModelProperty(value = "电池1充电（kWh）*100", position = 18)
    private Integer totalBat1Chgenergy;

    /** 电池1放电（kWh）*100 */
    @ApiModelProperty(value = "电池1放电（kWh）*100", position = 19)
    private Integer todayBat1Disenergy;

    /** 电池1放电（kWh）*100 */
    @ApiModelProperty(value = "电池1放电（kWh）*100", position = 20)
    private Integer monthBat1Disenergy;

    /** 电池1放电（kWh）*100 */
    @ApiModelProperty(value = "电池1放电（kWh）*100", position = 21)
    private Integer yearBat1Disenergy;

    /** 电池1放电（kWh）*100 */
    @ApiModelProperty(value = "电池1放电（kWh）*100", position = 22)
    private Integer totalBat1Disenergy;

    /** 电池2充电(kWh)*100 */
    @ApiModelProperty(value = "电池2充电(kWh)*100", position = 23)
    private Integer todayBat2Chgenergy;

    /** 电池2充电(kWh)*100 */
    @ApiModelProperty(value = "电池2充电(kWh)*100", position = 24)
    private Integer monthBat2Chgenergy;

    /** 电池2充电(kWh)*100 */
    @ApiModelProperty(value = "电池2充电(kWh)*100", position = 25)
    private Integer yearBat2Chgenergy;

    /** 电池2充电(kWh)*100 */
    @ApiModelProperty(value = "电池2充电(kWh)*100", position = 26)
    private Integer totalBat2Chgenergy;

    /** 电池2放电(kWh)*100 */
    @ApiModelProperty(value = "电池2放电(kWh)*100", position = 27)
    private Integer todayBat2Disenergy;

    /** 电池2放电(kWh)*100 */
    @ApiModelProperty(value = "电池2放电(kWh)*100", position = 28)
    private Integer monthBat2Disenergy;

    /** 电池2放电(kWh)*100 */
    @ApiModelProperty(value = "电池2放电(kWh)*100", position = 29)
    private Integer yearBat2Disenergy;

    /** 电池2放电(kWh)*100 */
    @ApiModelProperty(value = "电池2放电(kWh)*100", position = 30)
    private Integer totalBat2Disenergy;

    /** 电池3充电(kWh)*100 */
    @ApiModelProperty(value = "电池3充电(kWh)*100", position = 31)
    private Integer todayBat3Chgenergy;

    /** 电池3充电(kWh)*100 */
    @ApiModelProperty(value = "电池3充电(kWh)*100", position = 32)
    private Integer monthBat3Chgenergy;

    /** 电池3充电(kWh)*100 */
    @ApiModelProperty(value = "电池3充电(kWh)*100", position = 33)
    private Integer yearBat3Chgenergy;

    /** 电池3充电(kWh)*100 */
    @ApiModelProperty(value = "电池3充电(kWh)*100", position = 34)
    private Integer totalBat3Chgenergy;

    /** 电池3放电(kWh)*100 */
    @ApiModelProperty(value = "电池3放电(kWh)*100", position = 35)
    private Integer todayBat3Disenergy;

    /** 电池3放电(kWh)*100 */
    @ApiModelProperty(value = "电池3放电(kWh)*100", position = 36)
    private Integer monthBat3Disenergy;

    /** 电池3放电(kWh)*100 */
    @ApiModelProperty(value = "电池3放电(kWh)*100", position = 37)
    private Integer yearBat3Disenergy;

    /** 电池3放电(kWh)*100 */
    @ApiModelProperty(value = "电池3放电(kWh)*100", position = 38)
    private Integer totalBat3Disenergy;

    /** 离网输出 || R相输出电压(V)*100 */
    @ApiModelProperty(value = "离网输出 || R相输出电压(V)*100", position = 39)
    private Integer routVolt;

    /** 离网输出 || R相输出电流(A)*100 */
    @ApiModelProperty(value = "离网输出 || R相输出电流(A)*100", position = 40)
    private Integer routCurr;

    /** 离网输出 || R相输出频率(Hz)*100 */
    @ApiModelProperty(value = "离网输出 || R相输出频率(Hz)*100", position = 41)
    private Integer routFreq;

    /** 离网输出 || R相输出组件直流电压mV(mV)*100 */
    @ApiModelProperty(value = "离网输出 || R相输出组件直流电压mV(mV)*100", position = 42)
    private Integer routDvi;

    /** 离网输出 || R相输出有功功率(W) */
    @ApiModelProperty(value = "离网输出 || R相输出有功功率(W)", position = 43)
    private Integer routPowerWatt;

    /** 离网输出 || R相输出表观功率(W) */
    @ApiModelProperty(value = "离网输出 || R相输出表观功率(W)", position = 44)
    private Integer routPowerVa;

    /** 离网输出 || S相输出电压(V)*100 */
    @ApiModelProperty(value = "离网输出 || S相输出电压(V)*100", position = 45)
    private Integer soutVolt;

    /** 离网输出 || S相输出电流(A)*100 */
    @ApiModelProperty(value = "离网输出 || S相输出电流(A)*100", position = 46)
    private Integer soutCurr;

    /** 离网输出 || S相输出频率(Hz)*100 */
    @ApiModelProperty(value = "离网输出 || S相输出频率(Hz)*100", position = 47)
    private Integer soutFreq;

    /** 离网输出 || S相输出组件直流电压mV(mV)*100 */
    @ApiModelProperty(value = "离网输出 || S相输出组件直流电压mV(mV)*100", position = 48)
    private Integer soutDvi;

    /** 离网输出 || S相输出有功功率(W) */
    @ApiModelProperty(value = "离网输出 || S相输出有功功率(W)", position = 49)
    private Integer soutPowerWatt;

    /** 离网输出 || S相输出表观功率(W) */
    @ApiModelProperty(value = "离网输出 || S相输出表观功率(W)", position = 50)
    private Integer soutPowerVa;

    /** 离网输出 || T相输出电压(V)*100 */
    @ApiModelProperty(value = "离网输出 || T相输出电压(V)*100", position = 51)
    private Integer toutVolt;

    /** 离网输出 || T相输出电流(A)*100 */
    @ApiModelProperty(value = "离网输出 || T相输出电流(A)*100", position = 52)
    private Integer toutCurr;

    /** 离网输出 || T相输出频率(Hz)*100 */
    @ApiModelProperty(value = "离网输出 || T相输出频率(Hz)*100", position = 53)
    private Integer toutFreq;

    /** 离网输出 || T相输出组件直流电压mV(mV)*100 */
    @ApiModelProperty(value = "离网输出 || T相输出组件直流电压mV(mV)*100", position = 54)
    private Integer toutDvi;

    /** 离网输出 || T相输出有功功率(W) */
    @ApiModelProperty(value = "离网输出 || T相输出有功功率(W)", position = 55)
    private Integer toutPowerWatt;

    /** 离网输出 || T相输出表观功率(W) */
    @ApiModelProperty(value = "离网输出 || T相输出表观功率(W)", position = 56)
    private Integer toutPowerVa;

    /** pv输出交流电 || 日输出电量（kWh）*100 */
    @ApiModelProperty(value = "pv输出交流电 || 日输出电量（kWh）*100", position = 57)
    private Integer todayPvenergy;

    /** pv输出交流电 || 月输出电量（kWh）*100 */
    @ApiModelProperty(value = "pv输出交流电 || 月输出电量（kWh）*100", position = 58)
    private Integer monthPvenergy;

    /** pv输出交流电 || 年输出电量（kWh）*100 */
    @ApiModelProperty(value = "pv输出交流电 || 年输出电量（kWh）*100", position = 59)
    private Integer yearPvenergy;

    /** pv输出交流电 || 总输出电量（kWh）*100 */
    @ApiModelProperty(value = "pv输出交流电 || 总输出电量（kWh）*100", position = 60)
    private Integer totalPvenergy;

    /** 散热器温度（℃） */
    @ApiModelProperty(value = "散热器温度（℃）", position = 61)
    private String sinkTemp;

    /** 环境温度（℃） */
    @ApiModelProperty(value = "环境温度（℃）", position = 62)
    private String ambTemp;

    /** 电池温度（℃） */
    @ApiModelProperty(value = "电池温度（℃）", position = 63)
    private String batTemp;

    /** 电池电压（V）*100 */
    @ApiModelProperty(value = "电池电压（V）*100", position = 64)
    private Integer batVolt;

    /** 电池电流（A）*100 */
    @ApiModelProperty(value = "电池电流（A）*100", position = 65)
    private Integer batCurr;

    /** 电池功率（w） */
    @ApiModelProperty(value = "电池功率（w）", position = 66)
    private Integer batPower;

    /** 电池电量  % */
    @ApiModelProperty(value = "电池电量  %", position = 67)
    private Integer batEnergyPercent;

    /** 电池控制器电流1（A）*100 */
    @ApiModelProperty(value = "电池控制器电流1（A）*100", position = 68)
    private Integer batCurr1;

    /** 电池控制器电流2（A）*100 */
    @ApiModelProperty(value = "电池控制器电流2（A）*100", position = 69)
    private Integer batCurr2;

    /** 电池运行状态 */
    @ApiModelProperty(value = "电池运行状态", position = 70)
    private Integer batrunStatus;

    /** 电池运行信息 */
    @ApiModelProperty(value = "电池运行信息", position = 71)
    private String batrunInfo;

    /** 数据插入时间 */
    @ApiModelProperty(value = "数据插入时间", position = 72)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}