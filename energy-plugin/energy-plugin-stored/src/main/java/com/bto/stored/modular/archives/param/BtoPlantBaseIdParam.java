package com.bto.stored.modular.archives.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * 电站档案Id参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class BtoPlantBaseIdParam {

    /** 电站uid */
    @ApiModelProperty(value = "电站uid", required = true)
    @NotBlank(message = "plantUid不能为空")
    private String plantUid;
}