package com.bto.stored.modular.project.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.stored.modular.project.entity.BtoProject;
import com.bto.stored.modular.project.param.*;

import java.util.List;

/**
 * 项目Service接口
 *
 * <AUTHOR>
 */
public interface BtoProjectService extends IService<BtoProject> {

    /**
     * 获取项目分页
     *
     */
    Page<BtoProject> page(BtoProjectPageParam btoProjectPageParam);

    /**
     * 获取项目树
     *
     */
    List<Tree<String>> tree();

    /**
     * 添加项目
     *
     */
    void add(BtoProjectAddParam btoProjectAddParam);

    /**
     * 编辑项目
     *
     */
    void edit(BtoProjectEditParam btoProjectEditParam);

    /**
     * 删除项目
     *
     */
    void delete(List<BtoProjectIdParam> btoProjectIdParamList);

    /**
     * 获取项目详情
     *
     */
    BtoProject detail(BtoProjectIdParam btoProjectIdParam);

    /**
     * 获取项目详情
     *
     **/
    BtoProject queryEntity(String id);

    /**
     * 获取所有项目
     *
     **/
    List<BtoProject> getAllProjectList();

    /**
     * 根据项目全名称获取项目id，有则返回，无则创建
     *
     **/
    String getProjectIdByProjectFullNameWithCreate(String projectFullName);

    /**
     * 根据id获取父子数据列表
     *
     **/
    List<BtoProject> getParentAndChildListById(List<BtoProject> originDataList, String id, boolean includeSelf);

    /**
     * 根据id获取所有的子数据列表
     *
     **/
    List<BtoProject> getChildListById(List<BtoProject> originDataList, String id, boolean includeSelf);

    /**
     * 根据id获取所有的父数据列表
     *
     **/
    List<BtoProject> getParentListById(List<BtoProject> originDataList, String id, boolean includeSelf);

    /**
     * 根据id获取数据
     *
     **/
    BtoProject getById(List<BtoProject> originDataList, String id);

    /**
     * 根据id获取父数据
     *
     **/
    BtoProject getParentById(List<BtoProject> originDataList, String id);

    /**
     * 根据id获取子数据
     *
     **/
    BtoProject getChildById(List<BtoProject> originDataList, String id);

    /**
     * 获取项目树选择器
     *
     */
    List<Tree<String>> projectTreeSelector();

    /**
     * 获取项目列表选择器
     *
     **/
    Page<BtoProject> projectListSelector(BtoProjectSelectorProjectListParam btoProjectSelectorProjectListParam);

}