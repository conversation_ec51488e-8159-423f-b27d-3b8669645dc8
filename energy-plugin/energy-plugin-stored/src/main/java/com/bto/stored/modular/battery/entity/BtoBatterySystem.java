package com.bto.stored.modular.battery.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 储能（电池）系统数据实体
 *
 * <AUTHOR>
 **/
@Getter
@Setter
@TableName("bto_battery_system")
public class BtoBatterySystem {

    /** 自增id */
    @TableId
    @ApiModelProperty(value = "自增id", position = 1)
    private Long id;

    /** 逆变器SN */
    @ApiModelProperty(value = "逆变器SN", position = 2)
    private String inverterSn;

    /** 数据时间 */
    @ApiModelProperty(value = "数据时间", position = 3)
    private Date collectionTime;

    /** 逆变器工作模式（0：初始化，1：等待，2、运行3、离网4、电网有负荷模式、储能5、故障6、升级7、调试8、自检9、重置） */
    @ApiModelProperty(value = "逆变器工作模式（0：初始化，1：等待，2、运行3、离网4、电网有负荷模式、储能5、故障6、升级7、调试8、自检9、重置）", position = 4)
    private Integer mpvMode;

    /** 逆变器状态（） */
    @ApiModelProperty(value = "逆变器状态（）", position = 5)
    private Integer invrunStatus;

    /** 并网状态（） */
    @ApiModelProperty(value = "并网状态（）", position = 6)
    private Integer gridrunStatus;

    /** 电池运行状态（） */
    @ApiModelProperty(value = "电池运行状态（）", position = 7)
    private Integer batrunStatus;

    /** 光伏运行状态（） */
    @ApiModelProperty(value = "光伏运行状态（）", position = 8)
    private Integer pvrunStatus;

    /** 并网连接状态（异常） */
    @ApiModelProperty(value = "并网连接状态（异常）", position = 9)
    private Integer gridStatus;

    /** 自检状态（异常） */
    @ApiModelProperty(value = "自检状态（异常）", position = 10)
    private Integer selftestStatus;

    /** 自检结果（关机并保存） */
    @ApiModelProperty(value = "自检结果（关机并保存）", position = 11)
    private Integer selftestResults;

    /** +总线实时电压（V）*100 */
    @ApiModelProperty(value = "+总线实时电压（V）*100", position = 12)
    private Integer anodicallyBusvolt;

    /** -总线实时电压（V）*100 */
    @ApiModelProperty(value = "-总线实时电压（V）*100", position = 13)
    private Integer negativeBusvolt;

    /** 漏电电流（mA）*100 */
    @ApiModelProperty(value = "漏电电流（mA）*100", position = 14)
    private Integer gfci;

    /** 逆变器接口电网 | R相电压(V) */
    @ApiModelProperty(value = "逆变器接口电网 | R相电压(V)", position = 15)
    private Integer vacr;

    /** 逆变器接口电网 | R相电流(A) */
    @ApiModelProperty(value = "逆变器接口电网 | R相电流(A)", position = 16)
    private Integer iacr;

    /** 逆变器接口电网 | R相频率(Hz) */
    @ApiModelProperty(value = "逆变器接口电网 | R相频率(Hz)", position = 17)
    private Integer facr;

    /** 逆变器接口电网 | R相电网组件直流电流mA */
    @ApiModelProperty(value = "逆变器接口电网 | R相电网组件直流电流mA", position = 18)
    private Integer rgridDci;

    /** 逆变器接口电网 | R相电网有功功率(W) */
    @ApiModelProperty(value = "逆变器接口电网 | R相电网有功功率(W)", position = 19)
    private Integer rgridPowerWatt;

    /** 逆变器接口电网 | R相电网表观功率(W) */
    @ApiModelProperty(value = "逆变器接口电网 | R相电网表观功率(W)", position = 20)
    private Integer rgridPowerVa;

    /** 逆变器接口电网 | R相电网功率因数 */
    @ApiModelProperty(value = "逆变器接口电网 | R相电网功率因数", position = 21)
    private Integer rgridPowerPf;

    /** 逆变器接口电网 | S相电压(V) */
    @ApiModelProperty(value = "逆变器接口电网 | S相电压(V)", position = 22)
    private Integer sgridVolt;

    /** 逆变器接口电网 | S相电流(A) */
    @ApiModelProperty(value = "逆变器接口电网 | S相电流(A)", position = 23)
    private Integer sgridCurr;

    /** 逆变器接口电网 | S相频率(Hz) */
    @ApiModelProperty(value = "逆变器接口电网 | S相频率(Hz)", position = 24)
    private Integer sgridFreq;

    /** 逆变器接口电网 | S相电网组件直流电流mA */
    @ApiModelProperty(value = "逆变器接口电网 | S相电网组件直流电流mA", position = 25)
    private Integer sgridDci;

    /** 逆变器接口电网 | S相电网有功功率(W) */
    @ApiModelProperty(value = "逆变器接口电网 | S相电网有功功率(W)", position = 26)
    private Integer sgridPowerWatt;

    /** 逆变器接口电网 | S相电网表观功率(W) */
    @ApiModelProperty(value = "逆变器接口电网 | S相电网表观功率(W)", position = 27)
    private Integer sgridPowerVa;

    /** 逆变器接口电网 | S相电网功率因数 */
    @ApiModelProperty(value = "逆变器接口电网 | S相电网功率因数", position = 28)
    private Integer sgridPowerPf;

    /** 逆变器接口电网 | T相电压(V) */
    @ApiModelProperty(value = "逆变器接口电网 | T相电压(V)", position = 29)
    private Integer tgridVolt;

    /** 逆变器接口电网 | T相电流(A) */
    @ApiModelProperty(value = "逆变器接口电网 | T相电流(A)", position = 30)
    private Integer tgridCurr;

    /** 逆变器接口电网 | T相频率(Hz) */
    @ApiModelProperty(value = "逆变器接口电网 | T相频率(Hz)", position = 31)
    private Integer tgridFreq;

    /** 逆变器接口电网 | T相电网组件直流电流mA */
    @ApiModelProperty(value = "逆变器接口电网 | T相电网组件直流电流mA", position = 32)
    private Integer tgridDci;

    /** 逆变器接口电网 | T相电网有功功率(W) */
    @ApiModelProperty(value = "逆变器接口电网 | T相电网有功功率(W)", position = 33)
    private Integer tgridPowerWatt;

    /** 逆变器接口电网 | T相电网表观功率(W) */
    @ApiModelProperty(value = "逆变器接口电网 | T相电网表观功率(W)", position = 34)
    private Integer tgridPowerVa;

    /** 逆变器接口电网 | T相电网功率因数 */
    @ApiModelProperty(value = "逆变器接口电网 | T相电网功率因数", position = 35)
    private Integer tgridPowerPf;

    /** 主控制器电压（V）*100 */
    @ApiModelProperty(value = "主控制器电压（V）*100", position = 36)
    private Integer busVoltMaster;

    /** 从控制器电压（V）*100 */
    @ApiModelProperty(value = "从控制器电压（V）*100", position = 37)
    private Integer busVoltSlave;

    /** 光伏能量流的方向(是否有光伏发电)（1:光伏能量流量 0:PV无输出） */
    @ApiModelProperty(value = "光伏能量流的方向(是否有光伏发电)（1:光伏能量流量 0:PV无输出）", position = 38)
    private Integer pvDirection;

    /** 蓄电池能量流的方向（电池使用情况）（1:电池放电 0:没有电池能量流 -1：电池充电） */
    @ApiModelProperty(value = "蓄电池能量流的方向（电池使用情况）（1:电池放电 0:没有电池能量流 -1：电池充电）", position = 39)
    private Integer batteryDirection;

    /** 电网能量流的方向（电网使用情况）（1:电网输出 0:无电网能量流量 -1：电网输入） */
    @ApiModelProperty(value = "电网能量流的方向（电网使用情况）（1:电网输出 0:无电网能量流量 -1：电网输入）", position = 40)
    private Integer gridDirection;

    /** 从输出到负荷的能量流动方向（1:有输出到负载 0:无输出能量流） */
    @ApiModelProperty(value = "从输出到负荷的能量流动方向（1:有输出到负载 0:无输出能量流）", position = 41)
    private Integer outputDirection;

    /** 系统总负荷耗电（kWh）*100 */
    @ApiModelProperty(value = "系统总负荷耗电（kWh）*100", position = 42)
    private Integer sysTotalLoad;

    /** 光伏总功率 */
    @ApiModelProperty(value = "光伏总功率", position = 43)
    private Integer totalPvPower;

    /** 电池总功率 */
    @ApiModelProperty(value = "电池总功率", position = 44)
    private Integer totalBatteryPower;

    /** 电网总实际功率 */
    @ApiModelProperty(value = "电网总实际功率", position = 45)
    private Integer totalGridpowerWatt;

    /** 电网总表观功率 */
    @ApiModelProperty(value = "电网总表观功率", position = 46)
    private Integer totalGridpowerVa;

    /** 逆变器总实际功率 */
    @ApiModelProperty(value = "逆变器总实际功率", position = 47)
    private Integer totalInvpowerWatt;

    /** 逆变器总表观功率 */
    @ApiModelProperty(value = "逆变器总表观功率", position = 48)
    private Integer totalInvpowerVa;

    /** 备用总负载实际功率 */
    @ApiModelProperty(value = "备用总负载实际功率", position = 49)
    private Integer backupTotalLoadpowerWatt;

    /** 备用总负载视在功率 */
    @ApiModelProperty(value = "备用总负载视在功率", position = 50)
    private Integer backupTotalLoadpowerVa;

    /** 电网系统实际功率 */
    @ApiModelProperty(value = "电网系统实际功率", position = 51)
    private Integer sysGridpowerWall;

    /** 发电机总有功功率 */
    @ApiModelProperty(value = "发电机总有功功率", position = 52)
    private Integer genPowerWall;

    /** 发电机的总表观功率 */
    @ApiModelProperty(value = "发电机的总表观功率", position = 53)
    private Integer genPowerVa;

    /** pv输出交流电 - 日输出电量(kWh)*100 */
    @ApiModelProperty(value = "pv输出交流电 - 日输出电量(kWh)*100", position = 54)
    private Integer todayPvenergy;

    /** pv输出交流电 - 月输出电量(kWh)*100 */
    @ApiModelProperty(value = "pv输出交流电 - 月输出电量(kWh)*100", position = 55)
    private Integer monthPvenergy;

    /** pv输出交流电 - 年输出电量(kWh)*100 */
    @ApiModelProperty(value = "pv输出交流电 - 年输出电量(kWh)*100", position = 56)
    private Integer yearPvenergy;

    /** pv输出交流电 - 总输出电量(kWh)*100 */
    @ApiModelProperty(value = "pv输出交流电 - 总输出电量(kWh)*100", position = 57)
    private Integer totalPvenergy;

    /** 系统负载 - 日负载耗电(kWh)*100 */
    @ApiModelProperty(value = "系统负载 - 日负载耗电(kWh)*100", position = 58)
    private Integer todayTotalloadenergy;

    /** 系统负载 - 月负载耗电(kWh)*100 */
    @ApiModelProperty(value = "系统负载 - 月负载耗电(kWh)*100", position = 59)
    private Integer monthTotalloadenergy;

    /** 系统负载 - 年负载耗电(kWh)*100 */
    @ApiModelProperty(value = "系统负载 - 年负载耗电(kWh)*100", position = 60)
    private Integer yearTotalloadenergy;

    /** 系统负载 - 总负载耗电(kWh)*100 */
    @ApiModelProperty(value = "系统负载 - 总负载耗电(kWh)*100", position = 61)
    private Integer totalTotalloadenergy;

    /** 系统卖电量 - 日卖电量(kWh)*100 */
    @ApiModelProperty(value = "系统卖电量 - 日卖电量(kWh)*100", position = 62)
    private Integer todayFeedinenergy;

    /** 系统卖电量 - 月卖电量(kWh)*100 */
    @ApiModelProperty(value = "系统卖电量 - 月卖电量(kWh)*100", position = 63)
    private Integer monthFeedinenergy;

    /** 系统卖电量 - 年卖电量(kWh)*100 */
    @ApiModelProperty(value = "系统卖电量 - 年卖电量(kWh)*100", position = 64)
    private Integer yearFeedinenergy;

    /** 系统卖电量 - 总卖电量(kWh)*100 */
    @ApiModelProperty(value = "系统卖电量 - 总卖电量(kWh)*100", position = 65)
    private Integer totalFeedinenergy;

    /** 购电总额 - 日买电量(kWh)*100 */
    @ApiModelProperty(value = "购电总额 - 日买电量(kWh)*100", position = 66)
    private Integer todayVectorFeedinenergy;

    /** 购电总额 - 月买电量(kWh)*100 */
    @ApiModelProperty(value = "购电总额 - 月买电量(kWh)*100", position = 67)
    private Integer monthVectorFeedinenergy;

    /** 购电总额 - 年买电量(kWh)*100 */
    @ApiModelProperty(value = "购电总额 - 年买电量(kWh)*100", position = 68)
    private Integer yearVectorFeedinenergy;

    /** 购电总额 - 总买电量(kWh)*100 */
    @ApiModelProperty(value = "购电总额 - 总买电量(kWh)*100", position = 69)
    private Integer totalVectorFeedinenergy;

    /** 散热器温度（℃） */
    @ApiModelProperty(value = "散热器温度（℃）", position = 70)
    private String sinkTemp;

    /** 环境温度（℃） */
    @ApiModelProperty(value = "环境温度（℃）", position = 71)
    private String ambTemp;

    /** 电池温度（℃） */
    @ApiModelProperty(value = "电池温度（℃）", position = 72)
    private String batTemp;

    /** 电路板/从属设备的故障信息 */
    @ApiModelProperty(value = "电路板/从属设备的故障信息", position = 73)
    private String hfaultMsg;

    /** 主控制器错误消息 */
    @ApiModelProperty(value = "主控制器错误消息", position = 74)
    private String mfaultMsg;

    /** 主控制器错误消息2 */
    @ApiModelProperty(value = "主控制器错误消息2", position = 75)
    private String mfaultMsg2;

    /** 逆变器运行信息 */
    @ApiModelProperty(value = "逆变器运行信息", position = 76)
    private String invrunInfo;

    /** 并网运行信息 */
    @ApiModelProperty(value = "并网运行信息", position = 77)
    private String gridrunInfo;

    /** 电池运行信息 */
    @ApiModelProperty(value = "电池运行信息", position = 78)
    private String batrunInfo;

    /** 光伏运行信息 */
    @ApiModelProperty(value = "光伏运行信息", position = 79)
    private String pvrunInfo;

    /** 数据插入时间 */
    @ApiModelProperty(value = "数据插入时间", position = 80)
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;

}