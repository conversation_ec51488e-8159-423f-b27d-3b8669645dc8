package com.bto.stored.modular.archives.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 电站档案添加参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class BtoPlantBaseAddParam {

    /** 电站名称 */
    @ApiModelProperty(value = "电站名称", position = 2)
    private String plantName;

    /** 装机容量(以最小单位Wp存储)kWp*1000 */
    @ApiModelProperty(value = "装机容量(以最小单位Wp存储)kWp*1000", position = 3)
    private Integer plantCapacity;

    /** 电站朝向（0：不一致，1：一致，-1：无朝向） */
    @ApiModelProperty(value = "电站朝向（0：不一致，1：一致，-1：无朝向）", position = 4)
    private String orientation;

    /** 电站类型（0：并网，1：储能，2：混合，3：交流耦合） */
    @ApiModelProperty(value = "电站类型（0：并网，1：储能，2：混合，3：交流耦合）", position = 6)
    private Integer plantTypeId;

    /** 逆变器数量 */
    @ApiModelProperty(value = "逆变器数量", position = 7)
    private Integer inverterNum;

    /** 项目分类ID */
    @ApiModelProperty(value = "项目分类ID", position = 25)
    private Long projectSpecial;

    /** 国家 */
    @ApiModelProperty(value = "国家", position = 9)
    private String country;

    /** 省 */
    @ApiModelProperty(value = "省", position = 10)
    private String province;

    /** 市/州 */
    @ApiModelProperty(value = "市/州", position = 11)
    private String city;

    /** 县/区 */
    @ApiModelProperty(value = "县/区", position = 12)
    private String area;

    /** 镇/街道 */
    @ApiModelProperty(value = "镇/街道", position = 13)
    private String town;

    /** 详细地址 */
    @ApiModelProperty(value = "详细地址", position = 14)
    private String address;

    /** 经度 */
    @ApiModelProperty(value = "经度", position = 15)
    private String longitude;

    /** 纬度 */
    @ApiModelProperty(value = "纬度", position = 16)
    private String latitude;

    /** 出售自发电价（元） */
    @ApiModelProperty(value = "出售自发电价（元）", position = 23)
    private BigDecimal salePrice;

    /** 电表编号 */
    @ApiModelProperty(value = "电表编号", position = 26)
    private String meterId;

    /** 进件编号 */
    @ApiModelProperty(value = "进件编号", position = 27)
    private String orderId;

    /** 用户uid */
    @ApiModelProperty(value = "用户uid", required = true, position = 28)
    @NotBlank(message = "userUid不能为空")
    private String userUid;

    /** 电站图片 */
    @ApiModelProperty(value = "电站图片", position = 33)
    private String imageUrl;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 34)
    private String remarks;

    /** 来源（0：三晶，1：自营） */
    @ApiModelProperty(value = "来源（0：三晶，1：自营）", position = 35)
    private Integer source;

}