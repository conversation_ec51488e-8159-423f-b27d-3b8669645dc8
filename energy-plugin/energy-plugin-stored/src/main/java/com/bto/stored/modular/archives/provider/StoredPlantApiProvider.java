package com.bto.stored.modular.archives.provider;

import cn.hutool.core.util.ObjectUtil;
import com.bto.stored.api.StoredPlantApi;
import com.bto.stored.modular.archives.service.BtoPlantBaseService;
import com.bto.stored.modular.project.entity.BtoProject;
import com.bto.stored.modular.project.service.BtoProjectService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> by 2022 on 2024/8/19.
 */
@Service
public class StoredPlantApiProvider implements StoredPlantApi {

    @Resource
    private BtoPlantBaseService plantBaseService;

    @Resource
    private BtoProjectService projectService;

    @Override
    public List<String> getPlantIdListByUserId(String userId) {
        return plantBaseService.getPlantIdListByUserId(userId);
    }

    @Override
    public List<String> getChildIdListByParentId(String id, boolean includeSelf) {
        List<BtoProject> projectList = projectService.getParentAndChildListById(projectService.getAllProjectList(), id, includeSelf);
        if (ObjectUtil.isNotEmpty(projectList)) {
            return projectList.stream().map(BtoProject::getId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<String> getPlantIdListByProjectList(List<String> projectList) {
        return plantBaseService.getPlantIdListByProjectList(projectList);
    }
}