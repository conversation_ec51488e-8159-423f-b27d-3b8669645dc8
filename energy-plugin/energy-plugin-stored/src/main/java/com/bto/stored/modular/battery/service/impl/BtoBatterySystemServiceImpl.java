package com.bto.stored.modular.battery.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.bto.common.enums.CommonSortOrderEnum;
import com.bto.common.exception.CommonException;
import com.bto.common.page.CommonPageRequest;
import com.bto.stored.modular.battery.entity.BtoBatterySystem;
import com.bto.stored.modular.battery.mapper.BtoBatterySystemMapper;
import com.bto.stored.modular.battery.param.BtoBatterySystemIdParam;
import com.bto.stored.modular.battery.param.BtoBatterySystemPageParam;
import com.bto.stored.modular.battery.service.BtoBatterySystemService;

import java.util.List;

/**
 * 储能（电池）系统数据Service接口实现类
 *
 * <AUTHOR>
 **/
@Service
public class BtoBatterySystemServiceImpl extends ServiceImpl<BtoBatterySystemMapper, BtoBatterySystem> implements BtoBatterySystemService {

    @Override
    public Page<BtoBatterySystem> page(BtoBatterySystemPageParam btoBatterySystemPageParam) {
        QueryWrapper<BtoBatterySystem> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isAllNotEmpty(btoBatterySystemPageParam.getSortField(), btoBatterySystemPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(btoBatterySystemPageParam.getSortOrder());
            queryWrapper.orderBy(true, btoBatterySystemPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(btoBatterySystemPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BtoBatterySystem::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<BtoBatterySystem> detail(BtoBatterySystemIdParam btoBatterySystemIdParam) {
        queryEntity(btoBatterySystemIdParam.getInverterSn());
        // 使用QueryWrapper构建查询条件
        QueryWrapper<BtoBatterySystem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("inverter_sn", btoBatterySystemIdParam.getInverterSn());
        if (ObjectUtil.isNotEmpty(btoBatterySystemIdParam.getStartDate()) && ObjectUtil.isNotEmpty(btoBatterySystemIdParam.getEndDate())) {
            queryWrapper.between("DATE(collection_time)", btoBatterySystemIdParam.getStartDate().toString(), btoBatterySystemIdParam.getEndDate().toString());
        }
        List<BtoBatterySystem> list = this.list(queryWrapper);
        if (ObjectUtil.isEmpty(list)) {
            throw new CommonException("在指定时间范围内未找到电池数据，InverterSn值为：{}", btoBatterySystemIdParam.getInverterSn());
        }
        return list;

    }

    @Override
    public void queryEntity(String inverterSn) {
        LambdaQueryWrapper<BtoBatterySystem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BtoBatterySystem::getInverterSn, inverterSn);
        BtoBatterySystem btoBatterySystem = this.getOne(queryWrapper);
        if (ObjectUtil.isEmpty(btoBatterySystem)) {
            throw new CommonException("储能（电池）系统数据不存在，inverterSn值为：{}", inverterSn);
        }
    }
}