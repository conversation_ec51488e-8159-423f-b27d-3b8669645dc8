package com.bto.stored.modular.device.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.stored.modular.device.entity.BtoDevice;
import com.bto.stored.modular.device.param.BtoDeviceAddParam;
import com.bto.stored.modular.device.param.BtoDeviceEditParam;
import com.bto.stored.modular.device.param.BtoDeviceIdParam;
import com.bto.stored.modular.device.param.BtoDevicePageParam;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 设备信息Service接口
 *
 * <AUTHOR>
 **/
public interface BtoDeviceService extends IService<BtoDevice> {

    Page<BtoDevice> page(BtoDevicePageParam btoDevicePageParam);

    @Transactional(rollbackFor = Exception.class)
    void add(BtoDeviceAddParam btoDeviceAddParam);

    @Transactional(rollbackFor = Exception.class)
    void edit(BtoDeviceEditParam btoDeviceEditParam);

    @Transactional(rollbackFor = Exception.class)
    void delete(List<BtoDeviceIdParam> btoDeviceIdParamList);

    BtoDevice detail(BtoDeviceIdParam btoDeviceIdParam);

    /**
     * 获取设备信息分页
     *
     * <AUTHOR>
     **/
    BtoDevice queryEntity(String id);

    /**
     * 根据电站ID查询设备信息
     */
    List<BtoDevice> getDevicesByPlantId(String plantId);
}