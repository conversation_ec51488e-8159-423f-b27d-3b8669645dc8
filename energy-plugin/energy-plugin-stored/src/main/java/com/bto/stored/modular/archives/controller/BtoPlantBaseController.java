package com.bto.stored.modular.archives.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.stored.modular.archives.entity.BtoPlantBase;
import com.bto.stored.modular.archives.param.BtoPlantBaseAddParam;
import com.bto.stored.modular.archives.param.BtoPlantBaseEditParam;
import com.bto.stored.modular.archives.param.BtoPlantBaseIdParam;
import com.bto.stored.modular.archives.param.BtoPlantBasePageParam;
import com.bto.stored.modular.archives.service.BtoPlantBaseService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.bto.common.annotation.CommonLog;
import com.bto.common.pojo.CommonResult;
import com.bto.common.pojo.CommonValidList;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * 电站档案控制器
 *
 * <AUTHOR>
 */
@Api(tags = "电站档案控制器")
@ApiSupport(author = "BTO", order = 0)
@RestController
@Validated
public class BtoPlantBaseController {

    @Resource
    private BtoPlantBaseService btoPlantBaseService;

    /**
     * 获取电站档案分页
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取电站档案分页")
    @GetMapping("/stored/archives/page")
    public CommonResult<Page<BtoPlantBase>> page(BtoPlantBasePageParam btoPlantBasePageParam) {
        return CommonResult.data(btoPlantBaseService.page(btoPlantBasePageParam));
    }

    /**
     * 添加电站档案
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加电站档案")
    @CommonLog("添加电站档案")
    @PostMapping("/stored/archives/add")
    public CommonResult<String> add(@RequestBody @Valid BtoPlantBaseAddParam btoPlantBaseAddParam) {
        btoPlantBaseService.add(btoPlantBaseAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑电站档案
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑电站档案")
    @CommonLog("编辑电站档案")
    @PostMapping("/stored/archives/edit")
    public CommonResult<String> edit(@RequestBody @Valid BtoPlantBaseEditParam btoPlantBaseEditParam) {
        btoPlantBaseService.edit(btoPlantBaseEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除电站档案
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除电站档案")
    @CommonLog("删除电站档案")
    @PostMapping("/stored/archives/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                       CommonValidList<BtoPlantBaseIdParam> btoPlantBaseIdParamList) {
        btoPlantBaseService.delete(btoPlantBaseIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取电站档案详情
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取电站档案详情")
    @GetMapping("/stored/archives/detail")
    public CommonResult<BtoPlantBase> detail(@Valid BtoPlantBaseIdParam btoPlantBaseIdParam) {
        return CommonResult.data(btoPlantBaseService.detail(btoPlantBaseIdParam));
    }

}