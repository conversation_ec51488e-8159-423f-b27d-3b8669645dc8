package com.bto.stored.modular.statistics.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR> by 2022 on 2024/8/24.
 */
@Getter
@Setter
public class CarbonStatisticsVO {

    /** 节约标准煤量，单位：吨 */
    @ApiModelProperty(value = "节约标准煤量，单位：吨", position = 1)
    private double savedStandardCoal;

    /** CO2减排量，单位：吨 */
    @ApiModelProperty(value = "CO2减排量，单位：吨", position = 2)
    private double co2Reduction;

    /** 等效植树量，单位：立方米 */
    @ApiModelProperty(value = "等效植树量，单位：立方米", position = 3)
    private double equivalentTreePlantingVolume;

    /** 发电量，单位：kWh */
    @ApiModelProperty(value = "发电量，单位：kWh", position = 4)
    private double generatedPower;

    /** 自发自用电量，单位：kWh */
    @ApiModelProperty(value = "自发自用电量，单位：kWh", position = 5)
    private double selfConsumedPower;

    /** 卖电量，单位：kWh */
    @ApiModelProperty(value = "卖电量，单位：kWh", position = 6)
    private double soldPower;

    /** 用电量，单位：kWh */
    @ApiModelProperty(value = "用电量，单位：kWh", position = 7)
    private double consumedPower;

    /** 自给自足电量，单位：kWh */
    @ApiModelProperty(value = "自给自足电量，单位：kWh", position = 8)
    private double selfSufficientPower;

    /** 买电量，单位：kWh */
    @ApiModelProperty(value = "买电量，单位：kWh", position = 9)
    private double purchasedPower;

    public void setTestData() {

        setSavedStandardCoal(10.42);
        setCo2Reduction(25.96);
        setEquivalentTreePlantingVolume(46.17);

        setGeneratedPower(656.58);
        setSoldPower(226.62);
        setConsumedPower(50.0);
        setPurchasedPower(365.55);
        setSelfConsumedPower(0.0);
        setSelfSufficientPower(0.0);

    }
}
