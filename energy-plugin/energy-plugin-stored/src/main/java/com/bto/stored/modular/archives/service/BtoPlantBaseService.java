package com.bto.stored.modular.archives.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.stored.modular.archives.entity.BtoPlantBase;
import com.bto.stored.modular.archives.param.BtoPlantBaseAddParam;
import com.bto.stored.modular.archives.param.BtoPlantBaseEditParam;
import com.bto.stored.modular.archives.param.BtoPlantBaseIdParam;
import com.bto.stored.modular.archives.param.BtoPlantBasePageParam;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 电站档案Service接口
 *
 * <AUTHOR>
 **/
public interface BtoPlantBaseService extends IService<BtoPlantBase> {

    Page<BtoPlantBase> page(BtoPlantBasePageParam btoPlantBasePageParam);

    @Transactional(rollbackFor = Exception.class)
    void add(BtoPlantBaseAddParam btoPlantBaseAddParam);

    @Transactional(rollbackFor = Exception.class)
    void edit(BtoPlantBaseEditParam btoPlantBaseEditParam);

    @Transactional(rollbackFor = Exception.class)
    void delete(List<BtoPlantBaseIdParam> btoPlantBaseIdParamList);

    BtoPlantBase detail(BtoPlantBaseIdParam btoPlantBaseIdParam);

    /**
     * 获取电站档案分页
     *
     * <AUTHOR>
     **/
    BtoPlantBase queryEntity(String id);

    /**
     * 根据用户id查询电站档案id
     */
    List<String> getPlantIdListByUserId(String userId);

    List<String> getPlantIdListByProjectList(List<String> projectList);
}