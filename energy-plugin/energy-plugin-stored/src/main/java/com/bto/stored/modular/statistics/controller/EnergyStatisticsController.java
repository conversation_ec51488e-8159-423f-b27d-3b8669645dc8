package com.bto.stored.modular.statistics.controller;

import com.bto.common.pojo.CommonResult;
import com.bto.stored.modular.statistics.vo.AlarmStatisticsVO;
import com.bto.stored.modular.statistics.vo.BatteryStatisticsInfoVO;
import com.bto.stored.modular.statistics.vo.CarbonStatisticsVO;
import com.bto.stored.modular.statistics.vo.EnergyStatisticsInfoVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Random;

/**
 * <AUTHOR> by 2022 on 2024/8/24.
 */
@Api(tags = "能源统计控制器")
@ApiSupport(author = "BTO", order = 6)
@RestController
@Validated
public class EnergyStatisticsController {

    /**
     * 获取能源统计信息
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取能源统计信息")
    @GetMapping("/stored/statistics/info")
    public CommonResult<EnergyStatisticsInfoVO> getStatisticsInfo() {

        // test data
        EnergyStatisticsInfoVO energyStatisticsInfoVO = new EnergyStatisticsInfoVO();
        energyStatisticsInfoVO.setTestData();
        energyStatisticsInfoVO.setUpdateTime(setUpdateTimeRandom(energyStatisticsInfoVO.getUpdateTime()));
        return CommonResult.data(energyStatisticsInfoVO);
    }

    /**
     * 获取能源统计信息
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("获取电池统计信息")
    @GetMapping("/stored/statistics/battery")
    public CommonResult<BatteryStatisticsInfoVO> getBatteryStatisticsInfo() {

        // test data
        BatteryStatisticsInfoVO batteryStatisticsInfoVO = new BatteryStatisticsInfoVO();
        batteryStatisticsInfoVO.setTestData();
        return CommonResult.data(batteryStatisticsInfoVO);
    }

    /**
     * 获取能量和节能减排信息
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("获取能量和节能减排信息")
    @GetMapping("/stored/statistics/carbon")
    public CommonResult<CarbonStatisticsVO> getCarbonStatisticsInfo() {
        // test data
        CarbonStatisticsVO carbonStatisticsVO = new CarbonStatisticsVO();
        carbonStatisticsVO.setTestData();
        return CommonResult.data(carbonStatisticsVO);
    }

    /**
     * 获取能量和节能减排信息
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("获取告警统计信息")
    @GetMapping("/stored/statistics/alarm")
    public CommonResult<AlarmStatisticsVO> getAlarmStatisticsInfo() {
        // test data
        AlarmStatisticsVO alarmStatisticsVO = new AlarmStatisticsVO();
        alarmStatisticsVO.setTestData();
        return CommonResult.data(alarmStatisticsVO);
    }

    public LocalDateTime setUpdateTimeRandom(LocalDateTime updateTime) {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 生成一个-5到-10之间的随机数（分钟），表示5到10分钟前的时间
        Random random = new Random();
        int minutesAgo = random.nextInt(6) - 5;
        // 设置更新时间为当前时间的前N分钟
        return now.minusMinutes(Math.abs(minutesAgo));
    }
}
