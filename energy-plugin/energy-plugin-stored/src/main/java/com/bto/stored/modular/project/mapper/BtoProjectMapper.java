package com.bto.stored.modular.project.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bto.stored.modular.project.entity.BtoProject;
import org.apache.ibatis.annotations.Param;

/**
 * 组织Mapper接口
 *
 **/
public interface BtoProjectMapper extends BaseMapper<BtoProject> {

    /**
     * 删除数据并忽略插件（逻辑删除、租户拼接）
     *

     */
    @InterceptorIgnore(tenantLine = "true")
    void deleteIgnoreInterceptor(@Param("ew") LambdaQueryWrapper<BtoProject> lambdaQueryWrapper);
}