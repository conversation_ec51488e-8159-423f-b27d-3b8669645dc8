package com.bto.stored.modular.battery.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.stored.modular.battery.entity.BtoBattery;
import com.bto.stored.modular.battery.param.BtoBatteryIdParam;
import com.bto.stored.modular.battery.param.BtoBatteryPageParam;

import java.util.List;

/**
 * 电池数据Service接口
 *
 * <AUTHOR>
 **/
public interface BtoBatteryService extends IService<BtoBattery> {

    Page<BtoBattery> page(BtoBatteryPageParam btoBatteryPageParam);

    List<BtoBattery> detail(BtoBatteryIdParam btoBatteryIdParam);

    /**
     * 获取电池数据分页
     *
     * <AUTHOR>
     **/
    void queryEntity(String inverterSn);
}