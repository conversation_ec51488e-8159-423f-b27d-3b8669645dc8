package com.bto.stored.modular.battery.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bto.common.pojo.CommonResult;
import com.bto.stored.modular.battery.entity.BtoBattery;
import com.bto.stored.modular.battery.param.BtoBatteryIdParam;
import com.bto.stored.modular.battery.param.BtoBatteryPageParam;
import com.bto.stored.modular.battery.service.BtoBatteryService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 电池数据控制器
 *
 * <AUTHOR>
 */
@Api(tags = "电池数据控制器")
@ApiSupport(author = "BTO", order = 1)
@RestController
@Validated
public class BtoBatteryController {

    @Resource
    private BtoBatteryService btoBatteryService;

    /**
     * 获取电池数据分页
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取电池数据分页")
    @GetMapping("/stored/battery/page")
    public CommonResult<Page<BtoBattery>> page(BtoBatteryPageParam btoBatteryPageParam) {
        return CommonResult.data(btoBatteryService.page(btoBatteryPageParam));
    }

    /**
     * 获取电池数据详情
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("根据逆变器SN获取电池数据详情")
    @GetMapping("/stored/battery/detail")
    public CommonResult<List<BtoBattery>> detail(@Valid BtoBatteryIdParam btoBatteryIdParam) {
        return CommonResult.data(btoBatteryService.detail(btoBatteryIdParam));
    }
}