package com.bto.stored.modular.statistics.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Random;

/**
 * <AUTHOR> by 2022 on 2024/8/24.
 */
@Getter
@Setter
public class BatteryStatisticsInfoVO {
    /** 电池组数 */
    @ApiModelProperty(value = "电池组数", position = 1)
    private int batteryGroupCount;

    /** 电池簇数 */
    @ApiModelProperty(value = "电池簇数", position = 2)
    private int batteryClusterCount;

    /** 电池包数 */
    @ApiModelProperty(value = "电池包数", position = 3)
    private int batteryPackCount;

    /** 当前状态 */
    @ApiModelProperty(value = "当前状态", position = 4)
    private String currentStatus;

    /** 可用容量百分比 */
    @ApiModelProperty(value = "可用容量百分比", position = 5)
    private String availabilityCapacity;

    /** 可用时间（小时） */
    @ApiModelProperty(value = "可用时间（小时）", position = 6)
    private String availableTime;

    /** 当前电量（kWh） */
    @ApiModelProperty(value = "当前电量（kWh）", position = 7)
    private double currentEnergy;

    /** 功率（W） */
    @ApiModelProperty(value = "功率（W）", position = 8)
    private int power;

    /** 电池容量（kWh） */
    @ApiModelProperty(value = "电池容量（kWh）", position = 9)
    private double capacity;

    /** 健康状态 */
    @ApiModelProperty(value = "健康状态", position = 10)
    private String healthStatus;

    /** 单体最高温度（℃） */
    @ApiModelProperty(value = "单体最高温度（℃）", position = 11)
    private double maxCellTemperature;

    /** 单体最低温度（℃） */
    @ApiModelProperty(value = "单体最低温度（℃）", position = 12)
    private double minCellTemperature;

    public void setTestData() {
        // 电池组数
        setBatteryGroupCount(3);

        // 电池簇数
        setBatteryClusterCount(2);

        // 电池包数
        setBatteryPackCount(6);

        // 当前状态
        String[] statuses = {"正常运行", "离线", "充电中", "放电中"};
        setCurrentStatus(getRandomStatus(statuses));

        // 当前电量（kWh）
        setCurrentEnergy(getRandomInt(500, 1200));

        // 可用容量百分比
        double capacityPercentage = (getCurrentEnergy() / 1200.0) * 100;
        setAvailabilityCapacity(String.format("%.2f%%", capacityPercentage));

        // 可用时间（小时）
        int availableHours = (int) (capacityPercentage / 20);
        setAvailableTime(String.valueOf(availableHours));

        // 功率（W）
        setPower(getRandomInt(0, 10000));

        // 电池容量（kWh）
        setCapacity(1200.0);

        // 健康状态
        setHealthStatus("良好");

        // 单体最高温度（℃）
        setMaxCellTemperature(35.0);

        // 单体最低温度（℃）
        setMinCellTemperature(8.0);

    }

    // 辅助方法：生成指定范围内的随机整数
    private int getRandomInt(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }

    // 辅助方法：从给定的字符串数组中随机选择一个元素
    private String getRandomStatus(String[] statuses) {
        Random random = new Random();
        return statuses[random.nextInt(statuses.length)];
    }
}
