package com.bto.stored.modular.project.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 组织添加参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BtoProjectAddParam {

    /** 父id */
    @ApiModelProperty(value = "父id", required = true, position = 1)
    @NotBlank(message = "parentId不能为空")
    private String parentId;

    /** 项目名称 */
    @ApiModelProperty(value = "项目名称", required = true, position = 2)
    @NotBlank(message = "name不能为空")
    private String name;

    /** 标题 */
    @ApiModelProperty(value = "标题", required = true, position = 3)
    @NotBlank(message = "title不能为空")
    private String title;

    /** 系统LOGO */
    @ApiModelProperty(value = "系统LOGO", position = 4)
    @NotBlank(message = "sysLogo不能为空")
    private String sysLogo;

    /** 大屏LOGO */
    @ApiModelProperty(value = "大屏LOGO", position = 5)
    private String screenLogo;

    /** 排序码 */
    @ApiModelProperty(value = "排序码", required = true, position = 6)
    @NotNull(message = "sortCode不能为空")
    private Integer sortCode;

    /** 扩展JSON */
    @ApiModelProperty(value = "扩展JSON", position = 7)
    private String extJson;
}