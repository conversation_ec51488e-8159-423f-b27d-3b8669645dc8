package com.bto.stored.core.enums;

import lombok.Getter;

/**
 * 系统模块数据类型枚举
 *
 * <AUTHOR>
 */
@Getter
public enum StoredDataTypeEnum {

    /**
     * 电站档案
     */
    ARCHIVERS("ARCHIVERS"),

    /**
     * 电池
     */
    BATTERY("BATTERY"),

    /**
     * 设备
     */
    DEVICE("DEVICE"),

    /**
     * 逆变器
     */
    INVERTER("INVERTER"),

    /**
     * 项目
     */
    PROJECT("PROJECT");

    private final String value;

    StoredDataTypeEnum(String value) {
        this.value = value;
    }

}