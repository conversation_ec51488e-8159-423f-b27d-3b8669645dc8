package com.bto.stored.modular.battery.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.bto.common.enums.CommonSortOrderEnum;
import com.bto.common.exception.CommonException;
import com.bto.common.page.CommonPageRequest;
import com.bto.stored.modular.battery.entity.BtoBattery;
import com.bto.stored.modular.battery.mapper.BtoBatteryMapper;
import com.bto.stored.modular.battery.param.BtoBatteryIdParam;
import com.bto.stored.modular.battery.param.BtoBatteryPageParam;
import com.bto.stored.modular.battery.service.BtoBatteryService;

import java.util.List;

/**
 * 电池数据Service接口实现类
 *
 * <AUTHOR>
 **/
@Service
public class BtoBatteryServiceImpl extends ServiceImpl<BtoBatteryMapper, BtoBattery> implements BtoBatteryService {

    @Override
    public Page<BtoBattery> page(BtoBatteryPageParam btoBatteryPageParam) {
        QueryWrapper<BtoBattery> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isAllNotEmpty(btoBatteryPageParam.getSortField(), btoBatteryPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(btoBatteryPageParam.getSortOrder());
            queryWrapper.orderBy(true, btoBatteryPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()),
                    StrUtil.toUnderlineCase(btoBatteryPageParam.getSortField()));
        } else {
            queryWrapper.lambda().orderByAsc(BtoBattery::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Override
    public List<BtoBattery> detail(BtoBatteryIdParam btoBatteryIdParam) {
        queryEntity(btoBatteryIdParam.getInverterSn());
        // 使用QueryWrapper构建查询条件
        QueryWrapper<BtoBattery> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("inverter_sn", btoBatteryIdParam.getInverterSn());
        if (ObjectUtil.isNotEmpty(btoBatteryIdParam.getStartDate()) && ObjectUtil.isNotEmpty(btoBatteryIdParam.getEndDate())) {
            queryWrapper.between("DATE(collection_time)", btoBatteryIdParam.getStartDate().toString(), btoBatteryIdParam.getEndDate().toString());
        }
        List<BtoBattery> list = this.list(queryWrapper);
        if (ObjectUtil.isEmpty(list)) {
            throw new CommonException("在指定时间范围内未找到电池数据，InverterSn值为：{}", btoBatteryIdParam.getInverterSn());
        }
        return list;
    }

    @Override
    public void queryEntity(String inverterSn) {
        LambdaQueryWrapper<BtoBattery> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BtoBattery::getInverterSn, inverterSn);
        BtoBattery btoBattery = this.getOne(queryWrapper);
        if (ObjectUtil.isEmpty(btoBattery)) {
            throw new CommonException("电池数据不存在，inverterSn值为：{}", inverterSn);
        }
    }
}