package com.bto.stored.modular.archives.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 电站档案查询参数
 *
 * <AUTHOR>
 **/
@Getter
@Setter
public class BtoPlantBasePageParam {

    /** 当前页 */
    @ApiModelProperty(value = "当前页码")
    private Integer current;

    /** 每页条数 */
    @ApiModelProperty(value = "每页条数")
    private Integer size;

    /** 排序字段 */
    @ApiModelProperty(value = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @ApiModelProperty(value = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @ApiModelProperty(value = "关键词")
    private String searchKey;

    /** 电站名称 */
    @ApiModelProperty(value = "电站名称")
    private String plantName;

    /** 省 */
    @ApiModelProperty(value = "省")
    private String province;

    /** 市/州 */
    @ApiModelProperty(value = "市/州")
    private String city;

    /** 县/区 */
    @ApiModelProperty(value = "县/区")
    private String area;

    /** 镇/街道 */
    @ApiModelProperty(value = "镇/街道")
    private String town;

    /** 详细地址 */
    @ApiModelProperty(value = "详细地址")
    private String address;

}