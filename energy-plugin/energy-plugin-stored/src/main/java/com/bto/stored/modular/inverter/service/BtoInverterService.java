package com.bto.stored.modular.inverter.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bto.stored.modular.inverter.entity.BtoInverter;
import com.bto.stored.modular.inverter.param.BtoInverterIdParam;
import com.bto.stored.modular.inverter.param.BtoInverterPageParam;

import java.util.List;

/**
 * 逆变器实时数据数据Service接口
 *
 * <AUTHOR>
 **/
public interface BtoInverterService extends IService<BtoInverter> {

    Page<BtoInverter> page(BtoInverterPageParam btoInverterPageParam);

    List<BtoInverter> detail(BtoInverterIdParam btoInverterIdParam);
}