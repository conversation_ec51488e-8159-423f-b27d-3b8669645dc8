package com.bto.stored.modular.device.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.bto.common.annotation.CommonLog;
import com.bto.common.pojo.CommonResult;
import com.bto.common.pojo.CommonValidList;
import com.bto.stored.modular.device.entity.BtoDevice;
import com.bto.stored.modular.device.param.BtoDeviceAddParam;
import com.bto.stored.modular.device.param.BtoDeviceEditParam;
import com.bto.stored.modular.device.param.BtoDeviceIdParam;
import com.bto.stored.modular.device.param.BtoDevicePageParam;
import com.bto.stored.modular.device.service.BtoDeviceService;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 设备信息控制器
 *
 * <AUTHOR>
 */
@Api(tags = "设备信息控制器")
@ApiSupport(author = "BTO", order = 3)
@RestController
@Validated
public class BtoDeviceController {

    @Resource
    private BtoDeviceService btoDeviceService;

    /**
     * 获取设备信息分页
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取设备信息分页")
    @GetMapping("/stored/device/page")
    public CommonResult<Page<BtoDevice>> page(BtoDevicePageParam btoDevicePageParam) {
        return CommonResult.data(btoDeviceService.page(btoDevicePageParam));
    }

    /**
     * 添加设备信息
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("添加设备信息")
    @CommonLog("添加设备信息")
    @PostMapping("/stored/device/add")
    public CommonResult<String> add(@RequestBody @Valid BtoDeviceAddParam btoDeviceAddParam) {
        btoDeviceService.add(btoDeviceAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑设备信息
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("编辑设备信息")
    @CommonLog("编辑设备信息")
    @PostMapping("/stored/device/edit")
    public CommonResult<String> edit(@RequestBody @Valid BtoDeviceEditParam btoDeviceEditParam) {
        btoDeviceService.edit(btoDeviceEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除设备信息
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 4)
    @ApiOperation("删除设备信息")
    @CommonLog("删除设备信息")
    @PostMapping("/stored/device/delete")
    public CommonResult<String> delete(@RequestBody @Valid @NotEmpty(message = "集合不能为空")
                                       CommonValidList<BtoDeviceIdParam> btoDeviceIdParamList) {
        btoDeviceService.delete(btoDeviceIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取设备信息详情
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("根据电站ID获取设备信息详情")
    @GetMapping("/stored/device/getDevicesByPlantId/{plantId}")
    public CommonResult<List<BtoDevice>> getDevicesByPlantId(@PathVariable String plantId) {
        return CommonResult.data(btoDeviceService.getDevicesByPlantId(plantId));
    }
}