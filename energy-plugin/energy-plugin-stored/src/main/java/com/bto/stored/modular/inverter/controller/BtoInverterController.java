package com.bto.stored.modular.inverter.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bto.common.pojo.CommonResult;
import com.bto.stored.modular.inverter.entity.BtoInverter;
import com.bto.stored.modular.inverter.param.BtoInverterIdParam;
import com.bto.stored.modular.inverter.param.BtoInverterPageParam;
import com.bto.stored.modular.inverter.service.BtoInverterService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 逆变器实时数据数据控制器
 *
 * <AUTHOR>
 */
@Api(tags = "逆变器实时数据数据控制器")
@ApiSupport(author = "BTO", order = 4)
@RestController
@Validated
public class BtoInverterController {

    @Resource
    private BtoInverterService btoInverterService;

    /**
     * 获取逆变器实时数据数据分页
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取逆变器实时数据数据分页")
    @GetMapping("/stored/inverter/page")
    public CommonResult<Page<BtoInverter>> page(BtoInverterPageParam btoInverterPageParam) {
        return CommonResult.data(btoInverterService.page(btoInverterPageParam));
    }


    /**
     * 获取逆变器实时数据数据详情
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取逆变器实时数据数据详情")
    @GetMapping("/stored/inverter/detail")
    public CommonResult<List<BtoInverter>> detail(@Valid BtoInverterIdParam btoInverterIdParam) {
        return CommonResult.data(btoInverterService.detail(btoInverterIdParam));
    }
}