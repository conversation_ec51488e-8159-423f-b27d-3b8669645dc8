package com.bto.stored.modular.battery.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import com.bto.common.pojo.CommonResult;
import com.bto.stored.modular.battery.entity.BtoBatterySystem;
import com.bto.stored.modular.battery.param.BtoBatterySystemIdParam;
import com.bto.stored.modular.battery.param.BtoBatterySystemPageParam;
import com.bto.stored.modular.battery.service.BtoBatterySystemService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 储能（电池）系统数据控制器
 *
 * <AUTHOR>
 */
@Api(tags = "储能（电池）系统数据控制器")
@ApiSupport(author = "BTO", order = 2)
@RestController
@Validated
public class BtoBatterySystemController {

    @Resource
    private BtoBatterySystemService btoBatterySystemService;

    /**
     * 获取储能（电池）系统数据分页
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取储能（电池）系统数据分页")
    @GetMapping("/stored/systemBattery/page")
    public CommonResult<Page<BtoBatterySystem>> page(BtoBatterySystemPageParam btoBatterySystemPageParam) {
        return CommonResult.data(btoBatterySystemService.page(btoBatterySystemPageParam));
    }

    /**
     * 获取储能（电池）系统数据详情
     *
     * <AUTHOR>
     */
    @ApiOperationSupport(order = 5)
    @ApiOperation("获取储能（电池）系统数据详情")
    @GetMapping("/stored/systemBattery/detail")
    public CommonResult<List<BtoBatterySystem>> detail(@Valid BtoBatterySystemIdParam btoBatterySystemIdParam) {
        return CommonResult.data(btoBatterySystemService.detail(btoBatterySystemIdParam));
    }
}