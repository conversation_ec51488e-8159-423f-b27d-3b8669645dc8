# BTO 能源管理平台部署指南

## 环境准备

### 系统要求

- **操作系统**: Linux (推荐 CentOS 7+/Ubuntu 18.04+) 或 Windows Server
- **Java**: JDK 1.8 或更高版本
- **内存**: 最低 4GB，推荐 8GB+
- **磁盘**: 最低 50GB 可用空间
- **网络**: 稳定的网络连接

### 依赖软件

#### 1. MySQL 8.0+

**CentOS/RHEL 安装**:
```bash
# 下载MySQL官方Yum Repository
wget https://dev.mysql.com/get/mysql80-community-release-el7-3.noarch.rpm
sudo rpm -Uvh mysql80-community-release-el7-3.noarch.rpm

# 安装MySQL
sudo yum install mysql-community-server

# 启动MySQL服务
sudo systemctl start mysqld
sudo systemctl enable mysqld

# 获取临时密码
sudo grep 'temporary password' /var/log/mysqld.log

# 安全配置
sudo mysql_secure_installation
```

**Ubuntu 安装**:
```bash
# 更新包列表
sudo apt update

# 安装MySQL
sudo apt install mysql-server

# 安全配置
sudo mysql_secure_installation

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql
```

#### 2. Redis 6.0+

**CentOS/RHEL 安装**:
```bash
# 安装EPEL仓库
sudo yum install epel-release

# 安装Redis
sudo yum install redis

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis
```

**Ubuntu 安装**:
```bash
# 安装Redis
sudo apt install redis-server

# 启动服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### 3. Java 环境

**安装 OpenJDK 8**:
```bash
# CentOS/RHEL
sudo yum install java-1.8.0-openjdk java-1.8.0-openjdk-devel

# Ubuntu
sudo apt install openjdk-8-jdk

# 验证安装
java -version
javac -version
```

## 数据库配置

### 1. 创建数据库

```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE bto_energy_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'bto_energy'@'%' IDENTIFIED BY 'BtoEnergy@2024';

-- 授权
GRANT ALL PRIVILEGES ON bto_energy_management.* TO 'bto_energy'@'%';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 2. 导入数据库脚本

```bash
# 导入数据库结构和初始数据
mysql -u bto_energy -p bto_energy_management < sql/bto_energy_management.sql
```

### 3. MySQL 配置优化

编辑 `/etc/mysql/mysql.conf.d/mysqld.cnf` (Ubuntu) 或 `/etc/my.cnf` (CentOS):

```ini
[mysqld]
# 基础配置
port = 3306
bind-address = 0.0.0.0

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 内存配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M

# 连接配置
max_connections = 1000
max_user_connections = 800

# 查询缓存
query_cache_type = 1
query_cache_size = 128M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

重启MySQL服务:
```bash
sudo systemctl restart mysql
```

## Redis 配置

### 1. Redis 配置文件

编辑 `/etc/redis/redis.conf`:

```conf
# 网络配置
bind 0.0.0.0
port 6379
protected-mode yes
requirepass BtoRedis@2024

# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

appendonly yes
appendfsync everysec

# 日志配置
loglevel notice
logfile /var/log/redis/redis-server.log

# 数据库数量
databases 16
```

重启Redis服务:
```bash
sudo systemctl restart redis
```

## 应用部署

### 1. 编译打包

```bash
# 克隆项目
git clone [项目地址]
cd bto-energy

# 编译打包
mvn clean package -DskipTests

# 打包结果
ls energy-web-app/target/energy-web-app-1.0.0.jar
```

### 2. 配置文件

创建生产环境配置文件 `application-prod.properties`:

```properties
#########################################
# server configuration
#########################################
server.port=82

#########################################
# spring profiles configuration
#########################################
spring.profiles.active=prod

#########################################
# datasource configuration
#########################################
spring.datasource.dynamic.datasource.master.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.dynamic.datasource.master.url=jdbc:mysql://localhost:3306/bto_energy_management?useUnicode=true&characterEncoding=utf-8&useSSL=false&allowPublicKeyRetrieval=true&nullCatalogMeansCurrent=true&useInformationSchema=true
spring.datasource.dynamic.datasource.master.username=bto_energy
spring.datasource.dynamic.datasource.master.password=BtoEnergy@2024
spring.datasource.dynamic.strict=true

# druid配置
spring.datasource.dynamic.druid.initial-size=10
spring.datasource.dynamic.druid.max-active=50
spring.datasource.dynamic.druid.min-idle=10
spring.datasource.dynamic.druid.max-wait=60000

#########################################
# redis configuration
#########################################
spring.redis.database=7
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=BtoRedis@2024
spring.redis.timeout=10s

spring.redis.lettuce.pool.max-active=200
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.pool.max-idle=20
spring.redis.lettuce.pool.min-idle=5

#########################################
# logging configuration
#########################################
logging.level.com.bto=info
logging.level.root=warn
logging.file.name=/var/log/bto-energy/application.log
logging.file.max-size=100MB
logging.file.max-history=30

#########################################
# sa-token configuration
#########################################
sa-token.alone-redis.database=2
sa-token.alone-redis.host=${spring.redis.host}
sa-token.alone-redis.port=${spring.redis.port}
sa-token.alone-redis.password=${spring.redis.password}
```

### 3. 创建启动脚本

创建 `start.sh`:

```bash
#!/bin/bash

APP_NAME="bto-energy"
JAR_FILE="energy-web-app-1.0.0.jar"
PID_FILE="/var/run/${APP_NAME}.pid"
LOG_DIR="/var/log/bto-energy"
APP_DIR="/opt/bto-energy"

# 创建日志目录
mkdir -p ${LOG_DIR}

# JVM参数
JVM_OPTS="-Xms2g -Xmx4g"
JVM_OPTS="${JVM_OPTS} -XX:+UseG1GC"
JVM_OPTS="${JVM_OPTS} -XX:MaxGCPauseMillis=200"
JVM_OPTS="${JVM_OPTS} -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="${JVM_OPTS} -XX:HeapDumpPath=${LOG_DIR}/"
JVM_OPTS="${JVM_OPTS} -Dfile.encoding=UTF-8"
JVM_OPTS="${JVM_OPTS} -Duser.timezone=Asia/Shanghai"

# 应用参数
APP_OPTS="--spring.profiles.active=prod"
APP_OPTS="${APP_OPTS} --server.port=82"

# 启动应用
cd ${APP_DIR}
nohup java ${JVM_OPTS} -jar ${JAR_FILE} ${APP_OPTS} > ${LOG_DIR}/startup.log 2>&1 &

# 保存PID
echo $! > ${PID_FILE}

echo "应用启动中，PID: $(cat ${PID_FILE})"
echo "日志文件: ${LOG_DIR}/application.log"
```

创建 `stop.sh`:

```bash
#!/bin/bash

APP_NAME="bto-energy"
PID_FILE="/var/run/${APP_NAME}.pid"

if [ -f ${PID_FILE} ]; then
    PID=$(cat ${PID_FILE})
    if ps -p ${PID} > /dev/null; then
        echo "正在停止应用，PID: ${PID}"
        kill ${PID}
        
        # 等待进程结束
        for i in {1..30}; do
            if ! ps -p ${PID} > /dev/null; then
                echo "应用已停止"
                rm -f ${PID_FILE}
                exit 0
            fi
            sleep 1
        done
        
        # 强制杀死进程
        echo "强制停止应用"
        kill -9 ${PID}
        rm -f ${PID_FILE}
    else
        echo "应用未运行"
        rm -f ${PID_FILE}
    fi
else
    echo "PID文件不存在"
fi
```

### 4. 部署应用

```bash
# 创建应用目录
sudo mkdir -p /opt/bto-energy
sudo mkdir -p /var/log/bto-energy

# 复制文件
sudo cp energy-web-app/target/energy-web-app-1.0.0.jar /opt/bto-energy/
sudo cp application-prod.properties /opt/bto-energy/
sudo cp start.sh /opt/bto-energy/
sudo cp stop.sh /opt/bto-energy/

# 设置权限
sudo chmod +x /opt/bto-energy/*.sh
sudo chown -R bto:bto /opt/bto-energy
sudo chown -R bto:bto /var/log/bto-energy

# 启动应用
cd /opt/bto-energy
sudo -u bto ./start.sh
```

## 系统服务配置

### 1. 创建 Systemd 服务

创建 `/etc/systemd/system/bto-energy.service`:

```ini
[Unit]
Description=BTO Energy Management Platform
After=network.target mysql.service redis.service

[Service]
Type=forking
User=bto
Group=bto
WorkingDirectory=/opt/bto-energy
ExecStart=/opt/bto-energy/start.sh
ExecStop=/opt/bto-energy/stop.sh
PIDFile=/var/run/bto-energy.pid
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 2. 启用服务

```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable bto-energy

# 启动服务
sudo systemctl start bto-energy

# 查看状态
sudo systemctl status bto-energy

# 查看日志
sudo journalctl -u bto-energy -f
```

## Nginx 反向代理

### 1. 安装 Nginx

```bash
# CentOS/RHEL
sudo yum install nginx

# Ubuntu
sudo apt install nginx
```

### 2. 配置 Nginx

创建 `/etc/nginx/conf.d/bto-energy.conf`:

```nginx
upstream bto-energy-backend {
    server 127.0.0.1:82;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers on;
    
    # 日志配置
    access_log /var/log/nginx/bto-energy-access.log;
    error_log /var/log/nginx/bto-energy-error.log;
    
    # 客户端配置
    client_max_body_size 100M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # 代理配置
    location / {
        proxy_pass http://bto-energy-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲配置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://bto-energy-backend;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API接口
    location /api/ {
        proxy_pass http://bto-energy-backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. 启动 Nginx

```bash
# 测试配置
sudo nginx -t

# 启动Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 重新加载配置
sudo systemctl reload nginx
```

## 监控配置

### 1. 应用监控

在 `application-prod.properties` 中添加:

```properties
# Actuator监控
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true
```

### 2. 日志监控

配置日志轮转 `/etc/logrotate.d/bto-energy`:

```
/var/log/bto-energy/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 bto bto
    postrotate
        systemctl reload bto-energy
    endscript
}
```

## 备份策略

### 1. 数据库备份

创建备份脚本 `/opt/backup/mysql-backup.sh`:

```bash
#!/bin/bash

BACKUP_DIR="/opt/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="bto_energy_management"
DB_USER="bto_energy"
DB_PASS="BtoEnergy@2024"

mkdir -p ${BACKUP_DIR}

# 备份数据库
mysqldump -u${DB_USER} -p${DB_PASS} ${DB_NAME} > ${BACKUP_DIR}/${DB_NAME}_${DATE}.sql

# 压缩备份文件
gzip ${BACKUP_DIR}/${DB_NAME}_${DATE}.sql

# 删除7天前的备份
find ${BACKUP_DIR} -name "*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: ${DB_NAME}_${DATE}.sql.gz"
```

### 2. 定时备份

添加到crontab:

```bash
# 编辑crontab
sudo crontab -e

# 添加定时任务（每天凌晨2点备份）
0 2 * * * /opt/backup/mysql-backup.sh
```

## 故障排查

### 1. 常见问题

**应用无法启动**:
```bash
# 查看应用日志
tail -f /var/log/bto-energy/application.log

# 查看系统服务状态
sudo systemctl status bto-energy

# 查看端口占用
netstat -tlnp | grep 82
```

**数据库连接失败**:
```bash
# 测试数据库连接
mysql -u bto_energy -p -h localhost bto_energy_management

# 查看MySQL状态
sudo systemctl status mysql

# 查看MySQL错误日志
sudo tail -f /var/log/mysql/error.log
```

**Redis连接失败**:
```bash
# 测试Redis连接
redis-cli -h localhost -p 6379 -a BtoRedis@2024 ping

# 查看Redis状态
sudo systemctl status redis

# 查看Redis日志
sudo tail -f /var/log/redis/redis-server.log
```

### 2. 性能调优

**JVM调优**:
```bash
# 查看GC日志
-XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:/var/log/bto-energy/gc.log

# 内存分析
jmap -histo PID
jstat -gc PID 1s
```

**数据库调优**:
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看连接数
SHOW STATUS LIKE 'Threads_connected';

-- 查看缓存命中率
SHOW STATUS LIKE 'Qcache_hits';
SHOW STATUS LIKE 'Qcache_inserts';
```

这份部署指南涵盖了从环境准备到生产部署的完整流程，包括数据库配置、应用部署、系统服务配置、反向代理、监控和备份等关键环节。
