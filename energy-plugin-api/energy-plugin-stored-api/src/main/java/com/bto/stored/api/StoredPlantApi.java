package com.bto.stored.api;

import java.util.List;

/**
 * <AUTHOR> by zhb on 2024/8/19.
 */

public interface StoredPlantApi {

    /**
     * 根据用户id查询电站档案id
     */
    List<String> getPlantIdListByUserId(String userId);

    /**
     * 根据项目ID查询所有子项目ID
     */
    List<String> getChildIdListByParentId(String id, boolean includeSelf);

    /**
     * 根据项目ID集合查询电站ID集合
     */
    List<String> getPlantIdListByProjectList(List<String> projectList);
}