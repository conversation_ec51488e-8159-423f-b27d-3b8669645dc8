# BTO 能源管理平台技术指南

## 架构设计

### 整体架构

BTO能源管理平台采用分层架构设计，主要分为以下几层：

```
┌─────────────────────────────────────────┐
│              前端层 (Frontend)           │
│         Vue.js / React / 移动端          │
├─────────────────────────────────────────┤
│              接口层 (API Layer)          │
│            RESTful API / Swagger        │
├─────────────────────────────────────────┤
│             业务层 (Service Layer)       │
│          插件化业务模块 (Plugins)         │
├─────────────────────────────────────────┤
│             数据层 (Data Layer)          │
│        MyBatis-Plus / MySQL / Redis     │
├─────────────────────────────────────────┤
│            基础设施层 (Infrastructure)    │
│      Spring Boot / Spring Security      │
└─────────────────────────────────────────┘
```

### 插件化架构

系统采用插件化架构，每个功能模块都是独立的插件：

#### 插件结构
```
energy-plugin-[name]/
├── src/main/java/com/bto/[name]/
│   ├── modular/              # 业务模块
│   │   ├── [module]/
│   │   │   ├── controller/   # 控制器
│   │   │   ├── service/      # 服务层
│   │   │   ├── entity/       # 实体类
│   │   │   ├── param/        # 参数类
│   │   │   └── mapper/       # 数据访问层
│   └── core/                 # 核心配置
└── pom.xml                   # 依赖配置
```

#### 插件依赖关系
```
energy-plugin-[name]
├── energy-plugin-[name]-api  # 对外接口
├── energy-common             # 通用模块
└── 其他插件API (按需引入)
```

## 核心技术详解

### 1. 数据访问层

#### MyBatis-Plus配置
```java
@Configuration
@MapperScan(basePackages = {"com.bto.**.mapper"})
public class GlobalConfigure {
    
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }
    
    @Bean
    public MetaObjectHandler metaObjectHandler() {
        return new CommonMetaObjectHandler();
    }
}
```

#### 实体类设计规范
```java
@Getter
@Setter
@TableName("table_name")
public class Entity extends CommonEntity {
    
    @TableId
    @ApiModelProperty(value = "主键ID", position = 1)
    private String id;
    
    @ApiModelProperty(value = "字段描述", position = 2)
    private String fieldName;
    
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
    
    @TableField(fill = FieldFill.UPDATE)
    private Date updateTime;
}
```

### 2. 安全认证

#### Sa-Token配置
```java
@Configuration
public class AuthConfigure implements WebMvcConfigurer {
    
    @Bean("stpLogic")
    public StpLogic getStpLogic() {
        return new StpLogic(SaClientTypeEnum.B.getValue());
    }
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new SaInterceptor()).addPathPatterns("/**");
    }
}
```

#### 权限控制
```java
// 登录认证
@SaCheckLogin
public class Controller {
    
    // 角色认证
    @SaCheckRole("admin")
    public void adminMethod() {}
    
    // 权限认证
    @SaCheckPermission("user:add")
    public void addUser() {}
}
```

### 3. 缓存机制

#### Redis配置
```properties
# Redis基础配置
spring.redis.database=7
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.timeout=10s

# 连接池配置
spring.redis.lettuce.pool.max-active=200
spring.redis.lettuce.pool.max-wait=-1ms
spring.redis.lettuce.pool.max-idle=10
spring.redis.lettuce.pool.min-idle=0
```

#### 缓存操作
```java
@Component
public class CommonCacheOperator {
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    public void put(String key, Object value) {
        redisTemplate.boundValueOps(CACHE_KEY_PREFIX + key).set(value);
    }
    
    public Object get(String key) {
        return redisTemplate.boundValueOps(CACHE_KEY_PREFIX + key).get();
    }
}
```

### 4. 异常处理

#### 全局异常处理
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ResponseBody
    @ExceptionHandler
    public CommonResult<String> handleException(Exception e) {
        return GlobalExceptionUtil.getCommonResult(e);
    }
}
```

#### 自定义异常
```java
public class CommonException extends RuntimeException {
    private Integer code;
    private String msg;
    
    public CommonException(String msg, Object... arguments) {
        super(StrUtil.format(msg, arguments));
        this.code = 500;
        this.msg = StrUtil.format(msg, arguments);
    }
}
```

## 开发规范

### 1. 代码结构规范

#### Controller层
```java
@Api(tags = "功能模块控制器")
@ApiSupport(author = "BTO", order = 1)
@RestController
@Validated
public class ModuleController {
    
    @Resource
    private ModuleService moduleService;
    
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取分页数据")
    @GetMapping("/module/page")
    public CommonResult<Page<Entity>> page(PageParam pageParam) {
        return CommonResult.data(moduleService.page(pageParam));
    }
}
```

#### Service层
```java
public interface ModuleService extends IService<Entity> {
    Page<Entity> page(PageParam pageParam);
    void add(AddParam addParam);
    void edit(EditParam editParam);
    void delete(List<IdParam> idParamList);
    Entity detail(IdParam idParam);
}

@Service
public class ModuleServiceImpl extends ServiceImpl<ModuleMapper, Entity> 
    implements ModuleService {
    
    @Override
    public Page<Entity> page(PageParam pageParam) {
        // 实现分页查询逻辑
    }
}
```

### 2. 参数验证规范

#### 参数类设计
```java
@Getter
@Setter
public class AddParam {
    
    @ApiModelProperty(value = "字段名称", required = true, position = 1)
    @NotBlank(message = "字段名称不能为空")
    private String fieldName;
    
    @ApiModelProperty(value = "数值字段", position = 2)
    @Min(value = 0, message = "数值不能小于0")
    private Integer numberField;
}
```

### 3. 数据库设计规范

#### 表命名规范
- 表名使用小写字母和下划线
- 表名前缀：bto_
- 示例：bto_plant_base, bto_device, bto_battery

#### 字段命名规范
- 字段名使用小写字母和下划线
- 主键统一使用id
- 创建时间：create_time
- 更新时间：update_time
- 删除标记：delete_flag

#### 索引设计
```sql
-- 主键索引
PRIMARY KEY (`id`)

-- 唯一索引
UNIQUE KEY `uk_field_name` (`field_name`)

-- 普通索引
KEY `idx_create_time` (`create_time`)

-- 复合索引
KEY `idx_plant_device` (`plant_uid`, `device_type`)
```

## 部署配置

### 1. 环境配置

#### 开发环境 (application-local.properties)
```properties
# 数据库配置
spring.datasource.dynamic.datasource.master.url=*************************************************
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=123456

# Redis配置
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=

# 日志级别
logging.level.com.bto=debug
```

#### 生产环境 (application-prod.properties)
```properties
# 数据库配置
spring.datasource.dynamic.datasource.master.url=***********************************************
spring.datasource.dynamic.datasource.master.username=${DB_USERNAME}
spring.datasource.dynamic.datasource.master.password=${DB_PASSWORD}

# Redis配置
spring.redis.host=${REDIS_HOST}
spring.redis.port=${REDIS_PORT}
spring.redis.password=${REDIS_PASSWORD}

# 日志级别
logging.level.com.bto=info
```

### 2. Docker部署

#### Dockerfile
```dockerfile
FROM openjdk:8-jre-alpine

LABEL maintainer="<EMAIL>"

VOLUME /tmp

COPY energy-web-app/target/energy-web-app-1.0.0.jar app.jar

EXPOSE 82

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-jar", "/app.jar"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "82:82"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: bto_energy_management
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    
  redis:
    image: redis:6.2-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

## 监控运维

### 1. 应用监控

#### Actuator配置
```properties
# 开启监控端点
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
```

### 2. 日志管理

#### Logback配置
```xml
<configuration>
    <springProfile name="local">
        <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>${CONSOLE_LOG_PATTERN}</pattern>
                <charset>utf-8</charset>
            </encoder>
        </appender>
        
        <root level="info">
            <appender-ref ref="STDOUT"/>
        </root>
    </springProfile>
</configuration>
```

## 性能调优

### 1. JVM参数优化
```bash
java -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/logs/ \
     -jar energy-web-app-1.0.0.jar
```

### 2. 数据库优化
```sql
-- 慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 连接数配置
SET GLOBAL max_connections = 1000;
SET GLOBAL max_user_connections = 800;
```

### 3. Redis优化
```conf
# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
```
