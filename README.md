# BTO 能源管理平台

## 项目简介

BTO 能源管理平台是一个基于 Spring Boot 的企业级储能系统管理平台，专注于光伏储能设备的监控、管理和数据分析。平台采用插件化架构设计，支持多种储能设备的接入和管理，为用户提供全面的能源管理解决方案。

## 技术架构

### 核心技术栈

- **后端框架**: Spring Boot 2.5.12
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **ORM框架**: MyBatis-Plus 3.x
- **安全认证**: Sa-Token 1.31.0
- **API文档**: Knife4j (Swagger)
- **连接池**: Druid
- **模板引擎**: Beetl
- **工具库**: Hutool

### 架构特点

- **插件化架构**: 采用模块化设计，各功能模块独立开发和部署
- **前后端分离**: RESTful API 设计，支持多端接入
- **多租户支持**: 支持B端管理和C端用户双重认证体系
- **高可扩展性**: 基于Spring Boot生态，易于扩展和维护

## 项目结构

```
bto-energy/
├── energy-common/              # 通用基础模块
│   ├── 异常处理
│   ├── 结果封装
│   ├── 缓存操作
│   ├── 工具类
│   └── 基础配置
├── energy-plugin-api/          # 插件API接口模块
│   ├── energy-plugin-auth-api/     # 认证接口
│   ├── energy-plugin-biz-api/      # 业务接口
│   ├── energy-plugin-client-api/   # C端接口
│   ├── energy-plugin-dev-api/      # 开发工具接口
│   ├── energy-plugin-gen-api/      # 代码生成接口
│   ├── energy-plugin-mobile-api/   # 移动端接口
│   ├── energy-plugin-sys-api/      # 系统管理接口
│   └── energy-plugin-stored-api/   # 储能系统接口
├── energy-plugin/              # 插件实现模块
│   ├── energy-plugin-auth/         # 登录鉴权插件
│   ├── energy-plugin-biz/          # 业务功能插件
│   ├── energy-plugin-client/       # C端功能插件
│   ├── energy-plugin-dev/          # 开发工具插件
│   ├── energy-plugin-gen/          # 代码生成插件
│   ├── energy-plugin-mobile/       # 移动端管理插件
│   ├── energy-plugin-sys/          # 系统功能插件
│   └── energy-plugin-stored/       # 储能系统插件
├── energy-web-app/             # 主启动模块
├── sql/                        # 数据库脚本
└── pom.xml                     # 主项目配置
```

## 核心功能

### 1. 储能系统管理
- **电站档案管理**: 电站基本信息、装机容量、类型配置
- **设备信息管理**: 逆变器、运维器、电表等设备管理
- **电池数据监控**: 实时电池状态、充放电数据
- **逆变器监控**: 功率、发电量、运行状态监控
- **能源统计分析**: 发电量、用电量、节能减排统计

### 2. 系统管理
- **用户管理**: 用户账号、角色、权限管理
- **组织架构**: 部门、岗位、人员组织管理
- **菜单管理**: 系统菜单、按钮权限配置
- **角色权限**: 基于RBAC的权限控制体系
- **系统配置**: 参数配置、字典管理

### 3. 开发工具
- **代码生成**: 基于数据库表自动生成CRUD代码
- **文件管理**: 本地、阿里云OSS、腾讯云COS文件存储
- **邮件服务**: 邮件发送、模板管理
- **短信服务**: 短信发送、验证码管理

### 4. 业务功能
- **数据监控**: 实时数据采集和展示
- **告警管理**: 设备异常告警、处理流程
- **报表统计**: 各类业务报表和数据分析
- **移动端支持**: 移动端数据查看和管理

## 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd bto-energy
```

2. **数据库配置**
```bash
# 创建数据库
CREATE DATABASE bto_energy_management;

# 导入数据库脚本
mysql -u root -p bto_energy_management < sql/bto_energy_management.sql
```

3. **配置文件修改**
```properties
# 修改 energy-web-app/src/main/resources/application.properties

# 数据库配置
spring.datasource.dynamic.datasource.master.url=*************************************************
spring.datasource.dynamic.datasource.master.username=root
spring.datasource.dynamic.datasource.master.password=your_password

# Redis配置
spring.redis.host=127.0.0.1
spring.redis.port=6379
spring.redis.password=your_redis_password
```

4. **编译运行**
```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run -pl energy-web-app
```

5. **访问系统**
- 后端服务: http://localhost:82
- API文档: http://localhost:82/doc.html
- 用户名/密码: bto/bto2024

## 配置说明

### 数据库配置
- 支持MySQL数据库
- 使用Druid连接池
- 支持动态数据源
- 配置数据库监控

### 缓存配置
- Redis作为缓存存储
- 支持权限缓存与业务缓存分离
- 配置连接池参数

### 安全配置
- Sa-Token权限认证
- 支持B端和C端双重认证
- JWT Token管理
- 接口权限控制

## API文档

系统集成了Knife4j API文档，启动后访问 http://localhost:82/doc.html

### API分组
- **系统功能SYS**: 用户、角色、权限管理
- **储能功能STORED**: 设备、数据监控管理  
- **开发工具DEV**: 文件、配置管理
- **代码生成GEN**: 代码生成工具
- **C端功能CLIENT**: 客户端接口

## 部署说明

### 开发环境
```bash
# 使用Maven运行
mvn spring-boot:run -pl energy-web-app
```

### 生产环境
```bash
# 打包
mvn clean package -pl energy-web-app

# 运行
java -jar energy-web-app/target/energy-web-app-1.0.0.jar
```

### Docker部署
```dockerfile
FROM openjdk:8-jre-alpine
COPY energy-web-app/target/energy-web-app-1.0.0.jar app.jar
EXPOSE 82
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 开发指南

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理机制
- RESTful API设计规范

### 插件开发
1. 在对应的plugin-api模块定义接口
2. 在plugin模块实现具体功能
3. 在web-app模块引入插件依赖
4. 配置相关权限和菜单

### 数据库设计
- 统一字段命名规范
- 逻辑删除字段设计
- 审计字段自动填充
- 分表策略支持

## 常见问题

### Q: 启动时数据库连接失败？
A: 检查数据库配置信息，确保数据库服务正常运行，用户权限正确。

### Q: Redis连接异常？
A: 确认Redis服务状态，检查连接参数配置。

### Q: API文档无法访问？
A: 确认knife4j配置正确，检查是否启用了生产模式。

## 技术支持

- 官网: https://www.btosolar.com
- 邮箱: <EMAIL>
- 文档: [项目文档地址]

## 许可证

Apache License 2.0

## 数据模型

### 核心实体

#### 电站档案 (BtoPlantBase)
- 电站基本信息管理
- 装机容量配置
- 电站类型：并网、储能、混合、交流耦合
- 地理位置和联系信息

#### 设备信息 (BtoDevice)
- 设备类型：逆变器、运维器、电表、气象站、储能机等
- 设备状态监控
- 厂家信息和质保管理
- 激活时间和生命周期管理

#### 电池数据 (BtoBattery)
- 电池充放电状态监控
- 电池容量和健康度
- 温度和电压监控
- 能量流向分析

#### 逆变器数据 (BtoInverter)
- 实时功率监控
- 发电量统计（日/月/年/总）
- 运行状态和故障诊断
- 效率分析

### 系统管理实体

#### 用户管理 (SysUser)
- 用户基本信息
- 角色权限关联
- 登录状态管理
- 个人工作台配置

#### 角色管理 (SysRole)
- 角色定义和分类
- 权限资源关联
- 组织范围控制

#### 菜单资源 (SysMenu)
- 菜单树结构
- 按钮权限控制
- 模块化管理

## 监控指标

### 能源统计
- **发电量监控**: 实时功率、日发电量、月发电量、年发电量
- **用电量统计**: 负载功率、用电量分析
- **储能状态**: 电池充放电状态、剩余容量
- **节能减排**: 碳排放减少量、环保效益计算

### 设备状态
- **设备在线率**: 设备连接状态监控
- **故障告警**: 设备异常自动告警
- **运行效率**: 设备运行效率分析
- **维护提醒**: 设备保养和维护提醒

### 系统性能
- **数据采集频率**: 实时数据采集和存储
- **响应时间**: API接口响应性能
- **并发处理**: 多用户并发访问支持
- **数据准确性**: 数据校验和一致性保证

## 安全机制

### 认证授权
- **双重认证体系**: B端管理员和C端用户分离
- **JWT Token**: 无状态token认证
- **权限控制**: 基于RBAC的细粒度权限控制
- **会话管理**: 支持单点登录和会话超时

### 数据安全
- **数据加密**: 敏感数据加密存储
- **SQL注入防护**: MyBatis-Plus参数化查询
- **XSS防护**: 输入输出数据过滤
- **接口限流**: 防止恶意请求

### 操作审计
- **操作日志**: 关键操作记录和追踪
- **数据变更**: 数据修改历史记录
- **登录日志**: 用户登录行为记录
- **异常监控**: 系统异常自动记录

## 性能优化

### 数据库优化
- **连接池配置**: Druid连接池优化
- **索引设计**: 关键字段索引优化
- **分页查询**: 大数据量分页处理
- **读写分离**: 支持主从数据库配置

### 缓存策略
- **Redis缓存**: 热点数据缓存
- **权限缓存**: 用户权限信息缓存
- **配置缓存**: 系统配置参数缓存
- **查询缓存**: 频繁查询结果缓存

### 代码优化
- **异步处理**: 耗时操作异步执行
- **批量操作**: 数据批量处理优化
- **资源管理**: 连接和资源及时释放
- **内存管理**: 避免内存泄漏

## 扩展功能

### 第三方集成
- **文件存储**: 支持本地、阿里云OSS、腾讯云COS
- **消息通知**: 邮件、短信通知服务
- **第三方登录**: 支持微信、码云等第三方登录
- **支付接口**: 预留支付功能接口

### 移动端支持
- **响应式设计**: 支持移动端访问
- **移动端API**: 专门的移动端接口
- **离线功能**: 支持离线数据查看
- **推送通知**: 移动端消息推送

### 数据分析
- **报表生成**: 多维度数据报表
- **趋势分析**: 数据趋势预测
- **对比分析**: 历史数据对比
- **导出功能**: 支持Excel、PDF导出

## 更新日志

### v1.0.0 (2024-08-09)
- 初始版本发布
- 完成储能系统基础功能
- 实现用户权限管理
- 集成API文档系统
- 支持多种设备类型接入
- 实现实时数据监控
- 完成基础报表功能
